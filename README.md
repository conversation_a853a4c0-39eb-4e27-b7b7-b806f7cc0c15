# PromptCraft - AI 提示词生成平台

一个智能的AI提示词生成平台，帮助用户将简单想法转化为专业级提示词。

## 功能特性

- 🚀 **首页快速生成**: 无需配置，输入想法即可生成专业提示词
- 🎯 **双阶段处理**: 意图理解 → 提示词生成，确保结果精准
- 🔧 **高级生成**: 支持多种AI模型，可自定义配置
- 💡 **智能增强**: 将简单想法转化为结构化、专业的提示词
- 📚 **个人灵感库**: 保存和管理您的所有创意成果

## 快速开始

### 1. 环境配置

复制环境变量文件：
```bash
cp .env.example .env.local
```

配置必要的环境变量：
```bash
# 首页快速生成功能 (必需)
DEEPSEEK_API_KEY=your-deepseek-api-key

# 数据库配置 (可选，用于保存生成历史)
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

### 2. 获取 DeepSeek API Key

1. 访问 [DeepSeek 平台](https://platform.deepseek.com/)
2. 注册并登录账户
3. 在控制台中创建 API Key
4. 将 API Key 添加到 `.env.local` 文件中

### 3. 安装依赖

```bash
npm install
# 或
pnpm install
```

### 4. 启动开发服务器

```bash
npm run dev
# 或
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 使用方法

### 首页快速生成

1. 在首页找到"智能提示词生成器"区域
2. 输入您的简单想法，例如：
   - "渤海汽车年报"
   - "如何学习AI相关知识"
   - "制作产品使用说明书"
3. 点击"智能生成"按钮
4. 查看AI的意图理解和生成的专业提示词
5. 点击"复制"按钮复制结果

### 高级生成模式

1. 点击"进入应用"进入高级模式
2. 在配置页面设置您的AI模型
3. 在生成页面选择模型并生成提示词
4. 支持多种AI提供商：OpenAI、Google、DeepSeek等

## API 接口

### 简化生成接口

```typescript
POST /api/ai/simple-generate
Content-Type: application/json

{
  "input": "您的想法或需求"
}
```

响应：
```typescript
{
  "success": true,
  "data": {
    "intentAnalysis": "意图分析结果",
    "generatedPrompt": "生成的专业提示词"
  }
}
```

## 技术栈

- **前端**: Next.js 15, React, TypeScript
- **样式**: Tailwind CSS
- **动画**: Framer Motion
- **3D效果**: React Three Fiber
- **UI组件**: Radix UI
- **数据库**: Supabase (可选)
- **AI模型**: DeepSeek, OpenAI, Google等

## 目录结构

```
├── app/
│   ├── api/
│   │   ├── ai/
│   │   │   ├── generate/          # 完整生成接口
│   │   │   └── simple-generate/   # 简化生成接口
│   │   └── config/
│   ├── app/                       # 应用主页面
│   ├── login/                     # 登录页面
│   └── register/                  # 注册页面
├── components/                    # 组件库
└── lib/                          # 工具函数
```

## 开发计划

- [ ] 支持更多AI模型
- [ ] 添加提示词模板库
- [ ] 实现协作功能
- [ ] 移动端优化
- [ ] 多语言支持

## 许可证

MIT License 