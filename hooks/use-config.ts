import { useState, useEffect } from 'react'
import { getBrowserSupabase } from '@/lib/create-browser-supabase'
import { useUserStore } from '@/lib/user-store'

export interface AIModel {
  model: string
  title: string
  baseURL: string
  apiKey: string
  features: string[]
}

export interface AIProvider {
  provider: string
  models: AIModel[]
}

export interface ConfigData {
  providers: AIProvider[]
}

export function useConfig() {
  const [config, setConfig] = useState<ConfigData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { user } = useUserStore()
  const supabase = getBrowserSupabase()

  const loadConfig = async () => {
    if (!supabase || !user) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const { data, error: fetchError } = await supabase
        .from('user_ai_configs')
        .select('config_data')
        .eq('user_id', user.id)
        .single()

      if (fetchError && fetchError.code !== 'PGRST116') {
        throw fetchError
      }

      if (data) {
        setConfig(data.config_data as ConfigData)
      }
    } catch (err) {
      console.error('加载配置失败:', err)
      setError('加载配置失败')
    } finally {
      setLoading(false)
    }
  }

  const saveConfig = async (newConfig: ConfigData) => {
    if (!supabase || !user) {
      throw new Error('未登录或未配置数据库')
    }

    try {
      // 先检查用户是否已有配置
      const { data: existingConfig, error: checkError } = await supabase
        .from('user_ai_configs')
        .select('id')
        .eq('user_id', user.id)
        .single()

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError
      }

      let error
      if (existingConfig) {
        // 用户已有配置，执行更新
        const { error: updateError } = await supabase
          .from('user_ai_configs')
          .update({
            config_data: newConfig,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', user.id)
        error = updateError
      } else {
        // 用户没有配置，执行插入
        const { error: insertError } = await supabase
          .from('user_ai_configs')
          .insert({
            user_id: user.id,
            config_data: newConfig
          })
        error = insertError
      }

      if (error) throw error

      setConfig(newConfig)
      return true
    } catch (err) {
      console.error('保存配置失败:', err)
      throw err
    }
  }

  const getModelsByProvider = (providerName: string): AIModel[] => {
    if (!config) return []
    
    const provider = config.providers.find(p => p.provider === providerName)
    return provider?.models || []
  }

  const getAllModels = (): AIModel[] => {
    if (!config) return []
    
    return config.providers.flatMap(provider => provider.models)
  }

  const getModelByName = (modelName: string): AIModel | null => {
    const allModels = getAllModels()
    return allModels.find(model => model.model === modelName) || null
  }

  useEffect(() => {
    loadConfig()
  }, [user, supabase]) // eslint-disable-line react-hooks/exhaustive-deps

  return {
    config,
    loading,
    error,
    loadConfig,
    saveConfig,
    getModelsByProvider,
    getAllModels,
    getModelByName,
    setConfig
  }
} 