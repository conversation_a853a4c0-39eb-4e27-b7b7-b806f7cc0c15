# 实时数据看板系统说明

## 概述

本系统实现了一个实时的数据看板，用于显示平台上生成的提示词总数。系统包含以下核心组件：

## 系统架构

### 1. 数据库层
- **global_stats 表**: 存储全局统计数据
- **prompts 表**: 存储用户生成的提示词
- **触发器**: 自动更新统计数据

### 2. API 层
- **GET /api/stats/global**: 获取全局统计数据
- **POST /api/stats/global**: 手动更新统计数据

### 3. 前端组件
- **LiveStats**: 首页实时数据看板
- **StatsAdminPage**: 管理员统计数据管理页面

## 核心功能

### 实时数据获取
```typescript
// 前端组件自动每30秒刷新数据
const fetchStats = async () => {
  const response = await fetch("/api/stats/global");
  const data = await response.json();
  // 更新显示数据
};
```

### 数据一致性检查
系统会自动检查统计表中的数据与实际 prompts 表数据是否一致：
- ✅ **一致**: 数据正常，触发器工作正常
- ⚠️ **不一致**: 可能需要手动更新

### 数据源标识
- **database**: 从数据库获取的真实数据
- **default**: 使用默认数据（数据库中无记录）
- **fallback**: 系统错误时的备用数据

## 使用方法

### 1. 查看实时数据
访问首页即可看到实时数据看板，显示当前平台生成的提示词总数。

### 2. 管理员功能
访问 `/app/admin/stats` 页面（需要登录）查看详细的统计数据管理界面。

### 3. 数据验证
运行测试脚本验证数据完整性：
```bash
node scripts/test-stats-integrity.js
```

## 数据库设计

### global_stats 表结构
```sql
CREATE TABLE global_stats (
  id INT PRIMARY KEY DEFAULT 1,
  total_prompts_generated BIGINT DEFAULT 0,
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT single_row_check CHECK (id = 1)
);
```

### 触发器机制
```sql
-- 当插入新的 prompt 时自动更新统计
CREATE TRIGGER update_stats_on_prompt_insert
  AFTER INSERT ON prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_global_stats();
```

## API 接口详情

### GET /api/stats/global
获取全局统计数据

**响应示例**:
```json
{
  "total_prompts_generated": 14321,
  "last_updated": "2024-01-01T12:00:00Z",
  "real_time_count": 14321,
  "is_data_consistent": true,
  "data_source": "database"
}
```

### POST /api/stats/global
手动更新统计数据（需要登录）

**响应示例**:
```json
{
  "message": "统计数据已更新",
  "data": {
    "id": 1,
    "total_prompts_generated": 14321,
    "last_updated": "2024-01-01T12:00:00Z"
  },
  "actual_count": 14321
}
```

## 故障排除

### 问题1: 数据不一致
**症状**: 统计数据与实际数据不匹配
**解决方案**: 
1. 访问管理员页面手动更新
2. 或调用 POST /api/stats/global

### 问题2: 显示默认数据
**症状**: 数据源显示为 "default"
**原因**: 数据库中没有统计记录
**解决方案**: 运行数据库初始化脚本或手动插入记录

### 问题3: 触发器不工作
**症状**: 新增提示词后统计数据不更新
**解决方案**: 
1. 检查数据库触发器是否存在
2. 手动重新创建触发器
3. 使用手动更新功能

## 性能优化

### 1. 缓存策略
- 前端组件使用30秒缓存
- API 层可以添加 Redis 缓存

### 2. 数据库优化
- global_stats 表只有一行数据，查询非常快
- 使用触发器而不是定时任务，减少数据库负载

### 3. 错误处理
- 多层次降级：数据库 → 默认值 → 离线数据
- 用户始终能看到数据，即使系统部分故障

## 监控和维护

### 日常检查
1. 查看管理员页面确认数据一致性
2. 检查系统状态是否正常
3. 定期运行测试脚本

### 定期维护
1. 清理过期的测试数据
2. 检查触发器性能
3. 备份统计数据

## 扩展功能

### 未来可能的增强
1. 添加更多统计维度（按日期、用户类型等）
2. 实现 WebSocket 实时推送
3. 添加数据可视化图表
4. 支持多种统计指标

## 安全考虑

### 权限控制
- 统计数据公开可读
- 更新操作需要用户登录
- 管理员页面需要额外权限验证

### 数据保护
- 统计数据不包含用户隐私信息
- 仅统计数量，不暴露具体内容 