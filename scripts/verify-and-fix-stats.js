/**
 * 验证和修复统计数据的脚本
 * 使用方法: node scripts/verify-and-fix-stats.js
 */

const { createClient } = require('@supabase/supabase-js');

// 从环境变量读取 Supabase 配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 缺少 Supabase 配置环境变量');
  console.log('请确保设置了以下环境变量:');
  console.log('- NEXT_PUBLIC_SUPABASE_URL');
  console.log('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function main() {
  console.log('🔍 开始验证和修复统计数据...\n');

  try {
    // 1. 检查数据库连接
    console.log('1️⃣ 检查数据库连接...');
    const { data: testData, error: testError } = await supabase
      .from('global_stats')
      .select('*')
      .limit(1);

    if (testError) {
      console.error('❌ 数据库连接失败:', testError.message);
      return;
    }
    console.log('✅ 数据库连接正常\n');

    // 2. 获取实际的提示词统计
    console.log('2️⃣ 统计实际数据...');
    const [
      { count: totalCount },
      { count: enhancedCount },
      { count: generatedCount }
    ] = await Promise.all([
      supabase.from('prompts').select('*', { count: 'exact', head: true }),
      supabase.from('prompts').select('*', { count: 'exact', head: true }).eq('type', 'enhanced'),
      supabase.from('prompts').select('*', { count: 'exact', head: true }).eq('type', 'generated')
    ]);

    console.log(`📊 实际统计数据:`);
    console.log(`   总计: ${totalCount || 0}`);
    console.log(`   增强: ${enhancedCount || 0}`);
    console.log(`   生成: ${generatedCount || 0}\n`);

    // 3. 检查 global_stats 表
    console.log('3️⃣ 检查全局统计表...');
    const { data: globalStats, error: statsError } = await supabase
      .from('global_stats')
      .select('*')
      .single();

    if (statsError && statsError.code !== 'PGRST116') {
      console.error('❌ 获取全局统计失败:', statsError.message);
      return;
    }

    if (!globalStats) {
      console.log('⚠️  全局统计表为空，正在初始化...');
      const { error: insertError } = await supabase
        .from('global_stats')
        .insert({
          id: 1,
          total_prompts_generated: totalCount || 0,
          last_updated: new Date().toISOString()
        });

      if (insertError) {
        console.error('❌ 初始化全局统计失败:', insertError.message);
        return;
      }
      console.log('✅ 全局统计表已初始化');
    } else {
      console.log(`📈 全局统计表数据:`);
      console.log(`   记录总数: ${globalStats.total_prompts_generated}`);
      console.log(`   最后更新: ${globalStats.last_updated}`);
      
      const storedCount = globalStats.total_prompts_generated || 0;
      const actualCount = totalCount || 0;
      const isConsistent = Math.abs(storedCount - actualCount) <= 1;

      if (!isConsistent) {
        console.log(`⚠️  数据不一致! 存储: ${storedCount}, 实际: ${actualCount}`);
        console.log('🔧 正在修复...');
        
        const { error: updateError } = await supabase
          .from('global_stats')
          .update({
            total_prompts_generated: actualCount,
            last_updated: new Date().toISOString()
          })
          .eq('id', 1);

        if (updateError) {
          console.error('❌ 修复失败:', updateError.message);
          return;
        }
        console.log('✅ 数据已修复');
      } else {
        console.log('✅ 数据一致性正常');
      }
    }

    // 4. 检查触发器状态
    console.log('\n4️⃣ 检查触发器状态...');
    const { data: triggers, error: triggerError } = await supabase
      .rpc('get_trigger_info', {});

    if (triggerError) {
      console.log('⚠️  无法获取触发器信息 (这是正常的，可能没有权限)');
    } else {
      console.log('✅ 触发器检查完成');
    }

    // 5. 测试API端点
    console.log('\n5️⃣ 测试API端点...');
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/stats/global`);
      if (response.ok) {
        const data = await response.json();
        console.log('✅ API端点正常工作');
        console.log(`   API返回总数: ${data.total_prompts_generated}`);
        console.log(`   数据源: ${data.data_source}`);
        console.log(`   一致性: ${data.is_data_consistent ? '✅' : '⚠️'}`);
      } else {
        console.log('⚠️  API端点响应异常:', response.status);
      }
    } catch (apiError) {
      console.log('⚠️  无法测试API端点 (可能服务器未运行)');
    }

    // 6. 显示最近的活动
    console.log('\n6️⃣ 最近活动统计...');
    const { data: recentPrompts, error: recentError } = await supabase
      .from('prompts')
      .select('created_at, type')
      .order('created_at', { ascending: false })
      .limit(10);

    if (!recentError && recentPrompts && recentPrompts.length > 0) {
      console.log('📝 最近10条记录:');
      recentPrompts.forEach((prompt, index) => {
        const date = new Date(prompt.created_at).toLocaleString('zh-CN');
        console.log(`   ${index + 1}. ${prompt.type} - ${date}`);
      });
    } else {
      console.log('📝 暂无提示词记录');
    }

    // 7. 生成报告
    console.log('\n📋 验证报告:');
    console.log('='.repeat(50));
    console.log(`✅ 数据库连接: 正常`);
    console.log(`✅ 提示词总数: ${totalCount || 0}`);
    console.log(`✅ 增强类型: ${enhancedCount || 0}`);
    console.log(`✅ 生成类型: ${generatedCount || 0}`);
    console.log(`✅ 全局统计: ${globalStats ? '已设置' : '已初始化'}`);
    console.log(`✅ 数据一致性: ${Math.abs((globalStats?.total_prompts_generated || 0) - (totalCount || 0)) <= 1 ? '正常' : '已修复'}`);
    console.log('='.repeat(50));
    console.log('🎉 验证和修复完成!\n');

  } catch (error) {
    console.error('❌ 验证过程中出现错误:', error);
  }
}

// 执行主函数
main().catch(console.error); 