-- 修复数据库类型约束和触发器
-- 使用方法: 在Supabase SQL编辑器中执行此脚本

-- 1. 更新 prompts 表的 type 字段约束，支持更多类型
ALTER TABLE public.prompts 
DROP CONSTRAINT IF EXISTS prompts_type_check;

ALTER TABLE public.prompts 
ADD CONSTRAINT prompts_type_check 
CHECK (type IN ('enhanced', 'generated', 'simple_generated', 'test'));

-- 2. 更新触发器函数，支持 INSERT, UPDATE, DELETE 操作
CREATE OR REPLACE FUNCTION update_global_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- 重新计算所有提示词的总数
  UPDATE public.global_stats 
  SET 
    total_prompts_generated = (
      SELECT COUNT(*) 
      FROM public.prompts 
      WHERE type IN ('enhanced', 'generated', 'simple_generated')
    ),
    last_updated = NOW()
  WHERE id = 1;
  
  -- 如果 global_stats 表为空，则插入初始记录
  INSERT INTO public.global_stats (id, total_prompts_generated, last_updated)
  SELECT 1, 0, NOW()
  WHERE NOT EXISTS (SELECT 1 FROM public.global_stats WHERE id = 1);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 3. 重新创建触发器，支持所有操作
DROP TRIGGER IF EXISTS update_stats_on_prompt_insert ON public.prompts;
DROP TRIGGER IF EXISTS update_stats_on_prompt_update ON public.prompts;
DROP TRIGGER IF EXISTS update_stats_on_prompt_delete ON public.prompts;

-- INSERT 触发器
CREATE TRIGGER update_stats_on_prompt_insert
  AFTER INSERT ON public.prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_global_stats();

-- UPDATE 触发器（防止type字段被修改时统计不准确）
CREATE TRIGGER update_stats_on_prompt_update
  AFTER UPDATE ON public.prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_global_stats();

-- DELETE 触发器
CREATE TRIGGER update_stats_on_prompt_delete
  AFTER DELETE ON public.prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_global_stats();

-- 4. 手动初始化/修复统计数据
INSERT INTO public.global_stats (id, total_prompts_generated, last_updated)
VALUES (
  1, 
  (SELECT COUNT(*) FROM public.prompts WHERE type IN ('enhanced', 'generated', 'simple_generated')),
  NOW()
)
ON CONFLICT (id) 
DO UPDATE SET 
  total_prompts_generated = (SELECT COUNT(*) FROM public.prompts WHERE type IN ('enhanced', 'generated', 'simple_generated')),
  last_updated = NOW();

-- 5. 验证修复结果
SELECT 
  'global_stats' as table_name,
  total_prompts_generated,
  last_updated
FROM public.global_stats
WHERE id = 1

UNION ALL

SELECT 
  'actual_count' as table_name,
  COUNT(*)::bigint as total_prompts_generated,
  NOW() as last_updated
FROM public.prompts 
WHERE type IN ('enhanced', 'generated', 'simple_generated');

-- 6. 显示各种类型的统计
SELECT 
  type,
  COUNT(*) as count,
  MIN(created_at) as first_created,
  MAX(created_at) as last_created
FROM public.prompts 
GROUP BY type
ORDER BY count DESC; 