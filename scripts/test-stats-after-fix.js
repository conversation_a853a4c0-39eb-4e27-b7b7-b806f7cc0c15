/**
 * 测试统计数据修复后的状态
 * 使用 API 调用验证数据一致性
 */

async function testStatsAfterFix() {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
  
  console.log('🧪 测试统计数据修复效果...\n');

  try {
    // 1. 获取修复后的状态
    console.log('1️⃣ 检查修复后的统计状态...');
    const response = await fetch(`${baseUrl}/api/stats/global`);
    const data = await response.json();
    
    console.log('📊 API 返回数据:');
    console.log(`   存储的总数 (total_prompts_generated): ${data.total_prompts_generated}`);
    console.log(`   实时总数 (real_time_count): ${data.real_time_count}`);
    console.log(`   数据一致性: ${data.is_data_consistent ? '✅ 一致' : '❌ 不一致'}`);
    console.log(`   数据源: ${data.data_source}`);
    
    if (data.breakdown) {
      console.log('\n📈 详细统计:');
      console.log(`   增强类型: ${data.breakdown.enhanced}`);
      console.log(`   生成类型: ${data.breakdown.generated}`);
      console.log(`   总计: ${data.breakdown.total}`);
    }
    
    if (data.growth_rate !== undefined) {
      console.log(`   增长率: ${data.growth_rate} 个/天`);
    }
    
    if (data.last_updated) {
      const lastUpdated = new Date(data.last_updated);
      console.log(`   最后更新: ${lastUpdated.toLocaleString('zh-CN')}`);
    }

    // 2. 验证数据一致性
    console.log('\n2️⃣ 数据一致性验证:');
    if (data.is_data_consistent) {
      console.log('✅ 统计数据一致性正常');
      console.log('✅ total_prompts_generated 已正确更新');
    } else {
      console.log('❌ 数据仍然不一致');
      console.log(`   差异: ${Math.abs(data.total_prompts_generated - data.real_time_count)}`);
      console.log('建议: 需要在 Supabase 控制台执行 scripts/fix-stats-sql.sql');
    }

    // 3. 测试前端显示
    console.log('\n3️⃣ 前端显示测试:');
    console.log(`前端现在会显示: ${data.real_time_count} (使用 real_time_count)`);
    
    if (data.real_time_count === 0) {
      console.log('💡 提示: 当前没有提示词数据');
      console.log('   - 可以测试生成一些提示词来验证触发器是否工作');
      console.log('   - 或者前端会显示默认的展示数据');
    }

    // 4. 趋势数据检查
    if (data.recent_trend && data.recent_trend.length > 0) {
      console.log('\n📈 最近趋势数据:');
      const hasActivity = data.recent_trend.some(day => day.total > 0);
      if (hasActivity) {
        data.recent_trend.forEach(day => {
          if (day.total > 0) {
            console.log(`   ${day.date}: ${day.total} 个 (增强: ${day.enhanced}, 生成: ${day.generated})`);
          }
        });
      } else {
        console.log('   最近7天无活动');
      }
    }

    // 5. 总结
    console.log('\n📋 修复状态总结:');
    console.log('='.repeat(50));
    console.log(`✅ API 响应: 正常`);
    console.log(`${data.is_data_consistent ? '✅' : '❌'} 数据一致性: ${data.is_data_consistent ? '正常' : '异常'}`);
    console.log(`✅ 前端显示: ${data.real_time_count} (实时数据)`);
    console.log(`✅ 数据源: ${data.data_source}`);
    console.log('='.repeat(50));
    
    if (data.is_data_consistent) {
      console.log('🎉 统计数据修复成功！total_prompts_generated 现在会正确更新');
    } else {
      console.log('⚠️  仍需手动执行 SQL 修复脚本');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testStatsAfterFix().catch(console.error); 