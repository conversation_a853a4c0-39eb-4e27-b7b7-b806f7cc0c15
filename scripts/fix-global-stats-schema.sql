-- 修复全局统计表结构，支持完整的用户统计
-- 包括登录用户和匿名用户的详细分类统计

-- 1. 扩展全局统计表结构
ALTER TABLE public.global_stats 
ADD COLUMN IF NOT EXISTS total_enhanced_logged BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_generated_logged BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_enhanced_anonymous BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_generated_anonymous BIGINT DEFAULT 0;

-- 2. 添加注释说明各字段含义
COMMENT ON COLUMN public.global_stats.total_prompts_generated IS '总提示词数量（兼容性字段）';
COMMENT ON COLUMN public.global_stats.total_enhanced_logged IS '登录用户增强次数';
COMMENT ON COLUMN public.global_stats.total_generated_logged IS '登录用户生成次数';
COMMENT ON COLUMN public.global_stats.total_enhanced_anonymous IS '匿名用户增强次数';
COMMENT ON COLUMN public.global_stats.total_generated_anonymous IS '匿名用户生成次数';

-- 3. 创建改进的触发器函数，支持按类型统计
CREATE OR REPLACE FUNCTION update_global_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- 重新计算登录用户的统计数据
  UPDATE public.global_stats 
  SET 
    total_enhanced_logged = (SELECT COUNT(*) FROM public.prompts WHERE type = 'enhanced'),
    total_generated_logged = (SELECT COUNT(*) FROM public.prompts WHERE type = 'generated'),
    total_prompts_generated = (
      (SELECT COUNT(*) FROM public.prompts) + 
      COALESCE(total_enhanced_anonymous, 0) + 
      COALESCE(total_generated_anonymous, 0)
    ),
    last_updated = NOW()
  WHERE id = 1;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 4. 重新创建触发器，支持 INSERT, UPDATE, DELETE
DROP TRIGGER IF EXISTS update_stats_on_prompt_insert ON public.prompts;
DROP TRIGGER IF EXISTS update_stats_on_prompt_update ON public.prompts;
DROP TRIGGER IF EXISTS update_stats_on_prompt_delete ON public.prompts;

CREATE TRIGGER update_stats_on_prompt_insert
  AFTER INSERT ON public.prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_global_stats();

CREATE TRIGGER update_stats_on_prompt_update
  AFTER UPDATE ON public.prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_global_stats();

CREATE TRIGGER update_stats_on_prompt_delete
  AFTER DELETE ON public.prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_global_stats();

-- 5. 创建 RPC 函数用于安全更新匿名用户统计
CREATE OR REPLACE FUNCTION increment_anonymous_stats(
  stat_type TEXT,
  increment_value INTEGER DEFAULT 1
)
RETURNS VOID AS $$
BEGIN
  -- 验证统计类型
  IF stat_type NOT IN ('enhanced', 'generated') THEN
    RAISE EXCEPTION 'Invalid stat_type. Must be "enhanced" or "generated"';
  END IF;
  
  -- 更新对应的匿名统计
  IF stat_type = 'enhanced' THEN
    UPDATE public.global_stats 
    SET 
      total_enhanced_anonymous = COALESCE(total_enhanced_anonymous, 0) + increment_value,
      total_prompts_generated = COALESCE(total_prompts_generated, 0) + increment_value,
      last_updated = NOW()
    WHERE id = 1;
  ELSIF stat_type = 'generated' THEN
    UPDATE public.global_stats 
    SET 
      total_generated_anonymous = COALESCE(total_generated_anonymous, 0) + increment_value,
      total_prompts_generated = COALESCE(total_prompts_generated, 0) + increment_value,
      last_updated = NOW()
    WHERE id = 1;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. 初始化新字段的数据（基于现有 prompts 表）
UPDATE public.global_stats 
SET 
  total_enhanced_logged = (SELECT COUNT(*) FROM public.prompts WHERE type = 'enhanced'),
  total_generated_logged = (SELECT COUNT(*) FROM public.prompts WHERE type = 'generated'),
  total_enhanced_anonymous = COALESCE(total_enhanced_anonymous, 0),
  total_generated_anonymous = COALESCE(total_generated_anonymous, 0),
  last_updated = NOW()
WHERE id = 1;

-- 7. 更新 total_prompts_generated 为真实总数
UPDATE public.global_stats 
SET total_prompts_generated = (
  COALESCE(total_enhanced_logged, 0) + 
  COALESCE(total_generated_logged, 0) + 
  COALESCE(total_enhanced_anonymous, 0) + 
  COALESCE(total_generated_anonymous, 0)
)
WHERE id = 1;

-- 8. 创建数据一致性检查函数
CREATE OR REPLACE FUNCTION check_stats_consistency()
RETURNS TABLE(
  field_name TEXT,
  expected_value BIGINT,
  actual_value BIGINT,
  is_consistent BOOLEAN
) AS $$
DECLARE
  logged_enhanced BIGINT;
  logged_generated BIGINT;
  stats_record RECORD;
BEGIN
  -- 获取实际的 prompts 表统计
  SELECT 
    COUNT(*) FILTER (WHERE type = 'enhanced'),
    COUNT(*) FILTER (WHERE type = 'generated')
  INTO logged_enhanced, logged_generated
  FROM public.prompts;
  
  -- 获取 global_stats 表记录
  SELECT * INTO stats_record FROM public.global_stats WHERE id = 1;
  
  -- 返回一致性检查结果
  RETURN QUERY
  SELECT 
    'total_enhanced_logged'::TEXT,
    logged_enhanced,
    COALESCE(stats_record.total_enhanced_logged, 0),
    logged_enhanced = COALESCE(stats_record.total_enhanced_logged, 0);
    
  RETURN QUERY
  SELECT 
    'total_generated_logged'::TEXT,
    logged_generated,
    COALESCE(stats_record.total_generated_logged, 0),
    logged_generated = COALESCE(stats_record.total_generated_logged, 0);
    
  RETURN QUERY
  SELECT 
    'total_prompts_calculated'::TEXT,
    logged_enhanced + logged_generated + 
    COALESCE(stats_record.total_enhanced_anonymous, 0) + 
    COALESCE(stats_record.total_generated_anonymous, 0),
    COALESCE(stats_record.total_prompts_generated, 0),
    (logged_enhanced + logged_generated + 
     COALESCE(stats_record.total_enhanced_anonymous, 0) + 
     COALESCE(stats_record.total_generated_anonymous, 0)) = 
    COALESCE(stats_record.total_prompts_generated, 0);
END;
$$ LANGUAGE plpgsql;

-- 9. 运行一致性检查
SELECT * FROM check_stats_consistency();

-- 10. 显示最终的统计结果
SELECT 
  total_prompts_generated as "总提示词数",
  total_enhanced_logged as "登录用户增强",
  total_generated_logged as "登录用户生成", 
  total_enhanced_anonymous as "匿名用户增强",
  total_generated_anonymous as "匿名用户生成",
  (total_enhanced_logged + total_generated_logged) as "登录用户小计",
  (total_enhanced_anonymous + total_generated_anonymous) as "匿名用户小计",
  last_updated as "最后更新时间"
FROM public.global_stats 
WHERE id = 1; 