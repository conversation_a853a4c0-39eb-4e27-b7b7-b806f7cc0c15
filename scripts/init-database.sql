-- 1. 用户信息表 (与 Supabase Auth 的 users 表关联)
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  username TEXT,
  intent_model_name TEXT DEFAULT 'Gemini Pro',
  generation_model_name TEXT DEFAULT 'GPT-4o',
  encrypted_intent_api_key TEXT,
  encrypted_generation_api_key TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. 用户AI模型配置表
CREATE TABLE IF NOT EXISTS public.user_ai_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  config_data JSONB NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 添加唯一约束，确保每个用户只有一条配置记录
ALTER TABLE public.user_ai_configs ADD CONSTRAINT unique_user_config UNIQUE (user_id);

-- 3. 用户灵感库表
CREATE TABLE IF NOT EXISTS public.prompts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  source_text TEXT, -- 原始输入
  result_text TEXT NOT NULL, -- 增强或生成后的提示词
  type TEXT NOT NULL CHECK (type IN ('enhanced', 'generated')), -- 'enhanced' 或 'generated'
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. 全局统计表 (用于首页实时看板)
CREATE TABLE IF NOT EXISTS public.global_stats (
  id INT PRIMARY KEY DEFAULT 1,
  total_prompts_generated BIGINT DEFAULT 0,
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT single_row_check CHECK (id = 1)
);

-- 初始化全局统计表
INSERT INTO public.global_stats (id) VALUES (1) ON CONFLICT (id) DO NOTHING;

-- 开启所有表的行级安全策略 (RLS)
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_ai_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.prompts ENABLE ROW LEVEL SECURITY;
-- global_stats 可公开读取，但只能由后端更新
ALTER TABLE public.global_stats ENABLE ROW LEVEL SECURITY;

-- 定义RLS策略
-- 用户只能查看和编辑自己的资料
DROP POLICY IF EXISTS "Users can view and edit their own profile." ON public.profiles;
CREATE POLICY "Users can view and edit their own profile." ON public.profiles 
  FOR ALL USING (auth.uid() = id);

-- 用户只能管理自己的AI配置
DROP POLICY IF EXISTS "Users can manage their own AI configs." ON public.user_ai_configs;
CREATE POLICY "Users can manage their own AI configs." ON public.user_ai_configs 
  FOR ALL USING (auth.uid() = user_id);

-- 用户只能管理自己的提示词
DROP POLICY IF EXISTS "Users can manage their own prompts." ON public.prompts;
CREATE POLICY "Users can manage their own prompts." ON public.prompts 
  FOR ALL USING (auth.uid() = user_id);

-- 任何人都可以读取全局统计
DROP POLICY IF EXISTS "Anyone can read global stats." ON public.global_stats;
CREATE POLICY "Anyone can read global stats." ON public.global_stats 
  FOR SELECT USING (true);

-- 创建触发器函数来更新全局统计
CREATE OR REPLACE FUNCTION update_global_stats()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.global_stats 
  SET 
    total_prompts_generated = (SELECT COUNT(*) FROM public.prompts),
    last_updated = NOW()
  WHERE id = 1;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS update_stats_on_prompt_insert ON public.prompts;
CREATE TRIGGER update_stats_on_prompt_insert
  AFTER INSERT ON public.prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_global_stats();

-- 创建用户资料的触发器函数
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, username)
  VALUES (NEW.id, NEW.raw_user_meta_data->>'username');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建用户注册时自动创建资料的触发器
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user();

-- 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为用户配置表添加更新时间戳触发器
DROP TRIGGER IF EXISTS update_user_ai_configs_updated_at ON public.user_ai_configs;
CREATE TRIGGER update_user_ai_configs_updated_at
  BEFORE UPDATE ON public.user_ai_configs
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- 为用户资料表添加更新时间戳触发器
DROP TRIGGER IF EXISTS update_profiles_updated_at ON public.profiles;
CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
