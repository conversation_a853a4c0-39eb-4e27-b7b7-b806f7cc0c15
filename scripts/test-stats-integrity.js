/**
 * 测试统计数据完整性和触发器功能
 * 使用方法: node scripts/test-stats-integrity.js
 */

const { createClient } = require('@supabase/supabase-js');

// 从环境变量读取 Supabase 配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // 需要服务端密钥

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 缺少 Supabase 配置环境变量');
  console.log('请确保设置了以下环境变量:');
  console.log('- NEXT_PUBLIC_SUPABASE_URL');
  console.log('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testStatsIntegrity() {
  console.log('🔍 开始测试统计数据完整性...\n');

  try {
    // 1. 检查 global_stats 表是否存在
    console.log('1️⃣ 检查 global_stats 表...');
    const { data: globalStats, error: statsError } = await supabase
      .from('global_stats')
      .select('*')
      .single();

    if (statsError) {
      if (statsError.code === 'PGRST116') {
        console.log('⚠️  global_stats 表中没有数据，正在初始化...');
        
        // 初始化 global_stats 表
        const { error: initError } = await supabase
          .from('global_stats')
          .insert({ id: 1, total_prompts_generated: 0 });
        
        if (initError) {
          console.error('❌ 初始化 global_stats 表失败:', initError);
          return;
        }
        
        console.log('✅ global_stats 表已初始化');
      } else {
        console.error('❌ 获取 global_stats 数据失败:', statsError);
        return;
      }
    } else {
      console.log('✅ global_stats 表存在，当前数据:', globalStats);
    }

    // 2. 获取实际的 prompts 数量
    console.log('\n2️⃣ 统计实际的 prompts 数量...');
    const { count: actualCount, error: countError } = await supabase
      .from('prompts')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('❌ 获取 prompts 数量失败:', countError);
      return;
    }

    console.log(`✅ 实际 prompts 数量: ${actualCount}`);

    // 3. 比较数据一致性
    console.log('\n3️⃣ 检查数据一致性...');
    const { data: currentStats } = await supabase
      .from('global_stats')
      .select('total_prompts_generated')
      .single();

    const storedCount = currentStats?.total_prompts_generated || 0;
    console.log(`📊 存储的统计数量: ${storedCount}`);
    console.log(`📊 实际 prompts 数量: ${actualCount}`);

    if (storedCount === actualCount) {
      console.log('✅ 数据一致性检查通过');
    } else {
      console.log(`⚠️  数据不一致，差异: ${Math.abs(storedCount - actualCount)}`);
      
      // 手动更新统计数据
      console.log('🔧 正在手动更新统计数据...');
      const { error: updateError } = await supabase
        .from('global_stats')
        .update({ 
          total_prompts_generated: actualCount,
          last_updated: new Date().toISOString()
        })
        .eq('id', 1);

      if (updateError) {
        console.error('❌ 手动更新失败:', updateError);
      } else {
        console.log('✅ 统计数据已手动更新');
      }
    }

    // 4. 测试触发器功能（如果有权限）
    console.log('\n4️⃣ 测试触发器功能...');
    
    // 创建一个测试用户（如果不存在）
    let testUserId = '00000000-0000-0000-0000-000000000001'; // 固定的测试用户ID
    
    // 插入一条测试数据
    const testPrompt = {
      id: '99999999-9999-9999-9999-999999999999',
      user_id: testUserId,
      source_text: '测试触发器功能',
      result_text: '这是一个测试提示词，用于验证触发器是否正常工作',
      type: 'test'
    };

    console.log('📝 插入测试数据...');
    const { error: insertError } = await supabase
      .from('prompts')
      .upsert(testPrompt);

    if (insertError) {
      console.log('⚠️  无法插入测试数据（可能是权限问题）:', insertError.message);
    } else {
      console.log('✅ 测试数据已插入');
      
      // 等待触发器执行
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 检查统计数据是否更新
      const { data: updatedStats } = await supabase
        .from('global_stats')
        .select('total_prompts_generated, last_updated')
        .single();

      console.log('📊 触发器执行后的统计数据:', updatedStats);
      
      // 清理测试数据
      await supabase
        .from('prompts')
        .delete()
        .eq('id', testPrompt.id);
      
      console.log('🧹 测试数据已清理');
    }

    // 5. 检查 API 端点
    console.log('\n5️⃣ 测试 API 端点...');
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/stats/global`);
      const apiData = await response.json();
      
      if (response.ok) {
        console.log('✅ API 端点正常工作');
        console.log('📊 API 返回数据:', {
          total_prompts_generated: apiData.total_prompts_generated,
          data_source: apiData.data_source,
          is_data_consistent: apiData.is_data_consistent
        });
      } else {
        console.log('⚠️  API 端点返回错误:', apiData);
      }
    } catch (apiError) {
      console.log('⚠️  无法测试 API 端点（可能应用未运行）:', apiError.message);
    }

    console.log('\n🎉 统计数据完整性测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
testStatsIntegrity(); 