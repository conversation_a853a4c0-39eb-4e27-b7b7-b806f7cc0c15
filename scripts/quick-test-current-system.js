#!/usr/bin/env node

/**
 * 快速测试当前系统状态
 */

const API_BASE = 'http://localhost:3000';

async function makeRequest(url, options = {}) {
  try {
    const fetch = (await import('node-fetch')).default;
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    return { success: response.ok, data, status: response.status };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testCurrentSystem() {
  console.log('🔍 测试当前系统状态\n');
  
  // 1. 检查统计API
  console.log('1. 检查统计API...');
  const statsResult = await makeRequest(`${API_BASE}/api/stats/global`);
  
  if (statsResult.success) {
    console.log('✅ 统计API正常');
    console.log(`   总数: ${statsResult.data.total_prompts_generated}`);
    console.log(`   实时数: ${statsResult.data.real_time_count}`);
    console.log(`   一致性: ${statsResult.data.is_data_consistent ? '✅' : '❌'}`);
    
    // 检查是否有新字段
    if (statsResult.data.breakdown && statsResult.data.breakdown.logged_users) {
      console.log('✅ 新的统计结构已生效');
      console.log(`   登录用户: ${statsResult.data.breakdown.logged_users.total}`);
      console.log(`   匿名用户: ${statsResult.data.breakdown.anonymous_users.total}`);
    } else {
      console.log('⚠️  使用旧的统计结构，需要运行数据库升级脚本');
    }
  } else {
    console.log('❌ 统计API失败:', statsResult.error || statsResult.data);
  }
  
  // 2. 测试匿名生成
  console.log('\n2. 测试匿名生成...');
  const generateResult = await makeRequest(`${API_BASE}/api/ai/simple-generate`, {
    method: 'POST',
    body: JSON.stringify({
      input: "快速测试"
    })
  });
  
  if (generateResult.success) {
    console.log('✅ 匿名生成功能正常');
    const promptLength = generateResult.data.data.generatedPrompt.length;
    console.log(`   生成提示词长度: ${promptLength} 字符`);
  } else {
    console.log('❌ 匿名生成失败:', generateResult.error || generateResult.data);
  }
  
  // 3. 再次检查统计（看是否更新）
  console.log('\n3. 检查统计是否更新...');
  await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
  
  const updatedStatsResult = await makeRequest(`${API_BASE}/api/stats/global`);
  
  if (updatedStatsResult.success) {
    const oldCount = statsResult.success ? statsResult.data.real_time_count : 0;
    const newCount = updatedStatsResult.data.real_time_count;
    const change = newCount - oldCount;
    
    console.log(`   更新前: ${oldCount}`);
    console.log(`   更新后: ${newCount}`);
    console.log(`   变化: ${change > 0 ? '+' : ''}${change}`);
    
    if (change > 0) {
      console.log('✅ 统计正确更新');
    } else {
      console.log('⚠️  统计未更新，可能需要运行数据库升级脚本');
    }
  }
  
  console.log('\n📋 系统状态总结:');
  console.log('   - API 接口: 正常工作');
  console.log('   - 匿名生成: 功能正常');
  console.log('   - 统计更新: 需要检查数据库配置');
  
  console.log('\n📝 下一步操作:');
  console.log('   1. 在 Supabase SQL 编辑器中运行: scripts/fix-global-stats-schema.sql');
  console.log('   2. 检查环境变量: SUPABASE_SERVICE_ROLE_KEY');
  console.log('   3. 重新测试系统功能');
}

testCurrentSystem().catch(console.error); 