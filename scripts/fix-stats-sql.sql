-- 修复统计数据不一致问题
-- 在 Supabase SQL 编辑器中执行此脚本

-- 1. 检查当前状态
SELECT 
  'global_stats 表' as source,
  total_prompts_generated as count,
  last_updated
FROM public.global_stats
WHERE id = 1

UNION ALL

SELECT 
  'prompts 表实际统计' as source,
  COUNT(*)::bigint as count,
  NOW() as last_updated
FROM public.prompts;

-- 2. 手动修复统计数据
UPDATE public.global_stats 
SET 
  total_prompts_generated = (
    SELECT COUNT(*) 
    FROM public.prompts 
    WHERE type IN ('enhanced', 'generated', 'simple_generated')
  ),
  last_updated = NOW()
WHERE id = 1;

-- 3. 验证修复结果
SELECT 
  'global_stats 表 (修复后)' as source,
  total_prompts_generated as count,
  last_updated
FROM public.global_stats
WHERE id = 1

UNION ALL

SELECT 
  'prompts 表实际统计' as source,
  COUNT(*)::bigint as count,
  NOW() as last_updated
FROM public.prompts;

-- 4. 检查各类型的统计
SELECT 
  type,
  COUNT(*) as count,
  MIN(created_at) as first_created,
  MAX(created_at) as last_created
FROM public.prompts 
GROUP BY type
ORDER BY count DESC;

-- 5. 确保触发器函数存在且正确
CREATE OR REPLACE FUNCTION update_global_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- 重新计算所有提示词的总数
  UPDATE public.global_stats 
  SET 
    total_prompts_generated = (
      SELECT COUNT(*) 
      FROM public.prompts 
      WHERE type IN ('enhanced', 'generated', 'simple_generated')
    ),
    last_updated = NOW()
  WHERE id = 1;
  
  -- 如果 global_stats 表为空，则插入初始记录
  INSERT INTO public.global_stats (id, total_prompts_generated, last_updated)
  SELECT 1, 0, NOW()
  WHERE NOT EXISTS (SELECT 1 FROM public.global_stats WHERE id = 1);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 6. 重新创建所有触发器
DROP TRIGGER IF EXISTS update_stats_on_prompt_insert ON public.prompts;
DROP TRIGGER IF EXISTS update_stats_on_prompt_update ON public.prompts;
DROP TRIGGER IF EXISTS update_stats_on_prompt_delete ON public.prompts;

-- INSERT 触发器
CREATE TRIGGER update_stats_on_prompt_insert
  AFTER INSERT ON public.prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_global_stats();

-- UPDATE 触发器
CREATE TRIGGER update_stats_on_prompt_update
  AFTER UPDATE ON public.prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_global_stats();

-- DELETE 触发器
CREATE TRIGGER update_stats_on_prompt_delete
  AFTER DELETE ON public.prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_global_stats();

-- 7. 最终验证
SELECT 
  'global_stats' as table_name,
  total_prompts_generated,
  last_updated,
  CASE 
    WHEN total_prompts_generated = (SELECT COUNT(*) FROM public.prompts) 
    THEN '✅ 数据一致' 
    ELSE '❌ 数据不一致' 
  END as consistency_check
FROM public.global_stats
WHERE id = 1; 