#!/usr/bin/env node

/**
 * 完整的统计系统测试脚本
 * 测试登录用户和匿名用户的统计功能
 */

const API_BASE = 'http://localhost:3000';

// 测试配置
const TEST_CONFIG = {
  // 测试用的简单输入
  simpleInput: "如何学习JavaScript",
  
  // 测试用的登录用户数据（需要真实的认证token）
  authToken: null, // 如果有登录token可以填入
  
  // 测试次数
  anonymousTestCount: 3,
  loggedTestCount: 2
};

/**
 * 发送HTTP请求的工具函数
 */
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    return { success: response.ok, data, status: response.status };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * 获取当前统计数据
 */
async function getCurrentStats() {
  console.log('\n📊 获取当前统计数据...');
  const result = await makeRequest(`${API_BASE}/api/stats/global`);
  
  if (result.success) {
    console.log('✅ 统计数据获取成功:');
    console.log(`   总数: ${result.data.total_prompts_generated}`);
    console.log(`   实时统计: ${result.data.real_time_count}`);
    console.log(`   数据一致性: ${result.data.is_data_consistent ? '✅' : '❌'}`);
    
    if (result.data.breakdown) {
      console.log('   详细分解:');
      console.log(`     增强: ${result.data.breakdown.enhanced}`);
      console.log(`     生成: ${result.data.breakdown.generated}`);
      
      if (result.data.breakdown.logged_users) {
        console.log(`     登录用户: ${result.data.breakdown.logged_users.total}`);
        console.log(`     匿名用户: ${result.data.breakdown.anonymous_users.total}`);
      }
    }
    
    return result.data;
  } else {
    console.log('❌ 获取统计数据失败:', result.error || result.data);
    return null;
  }
}

/**
 * 测试匿名用户生成
 */
async function testAnonymousGeneration() {
  console.log('\n🔄 测试匿名用户生成...');
  
  for (let i = 1; i <= TEST_CONFIG.anonymousTestCount; i++) {
    console.log(`\n   测试 ${i}/${TEST_CONFIG.anonymousTestCount}:`);
    
    const result = await makeRequest(`${API_BASE}/api/ai/simple-generate`, {
      method: 'POST',
      body: JSON.stringify({
        input: `${TEST_CONFIG.simpleInput} - 测试${i}`
      })
    });
    
    if (result.success) {
      console.log(`   ✅ 匿名生成成功 (${result.data.data.generatedPrompt.length} 字符)`);
    } else {
      console.log(`   ❌ 匿名生成失败:`, result.error || result.data);
    }
    
    // 短暂延迟
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

/**
 * 测试登录用户生成（如果有认证token）
 */
async function testLoggedUserGeneration() {
  if (!TEST_CONFIG.authToken) {
    console.log('\n⚠️  跳过登录用户测试（未提供认证token）');
    return;
  }
  
  console.log('\n🔄 测试登录用户生成...');
  
  for (let i = 1; i <= TEST_CONFIG.loggedTestCount; i++) {
    console.log(`\n   测试 ${i}/${TEST_CONFIG.loggedTestCount}:`);
    
    const result = await makeRequest(`${API_BASE}/api/ai/generate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TEST_CONFIG.authToken}`
      },
      body: JSON.stringify({
        input: `${TEST_CONFIG.simpleInput} - 登录测试${i}`,
        provider: 'deepseek',
        model: 'deepseek-chat'
      })
    });
    
    if (result.success) {
      console.log(`   ✅ 登录用户生成成功`);
    } else {
      console.log(`   ❌ 登录用户生成失败:`, result.error || result.data);
    }
    
    // 短暂延迟
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

/**
 * 测试数据一致性
 */
async function testDataConsistency() {
  console.log('\n🔍 测试数据一致性...');
  
  const stats = await getCurrentStats();
  if (!stats) return;
  
  if (stats.is_data_consistent) {
    console.log('✅ 数据一致性检查通过');
  } else {
    console.log('❌ 数据一致性检查失败');
    console.log(`   存储值: ${stats.total_prompts_generated}`);
    console.log(`   实时值: ${stats.real_time_count}`);
  }
}

/**
 * 测试手动统计更新（需要登录）
 */
async function testManualStatsUpdate() {
  if (!TEST_CONFIG.authToken) {
    console.log('\n⚠️  跳过手动统计更新测试（未提供认证token）');
    return;
  }
  
  console.log('\n🔄 测试手动统计更新...');
  
  const result = await makeRequest(`${API_BASE}/api/stats/global`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${TEST_CONFIG.authToken}`
    }
  });
  
  if (result.success) {
    console.log('✅ 手动统计更新成功');
    console.log(`   登录用户数据: ${result.data.logged_user_count}`);
    console.log(`   匿名用户数据: ${result.data.anonymous_count}`);
    console.log(`   总计: ${result.data.total_count}`);
  } else {
    console.log('❌ 手动统计更新失败:', result.error || result.data);
  }
}

/**
 * 主测试流程
 */
async function runTests() {
  console.log('🚀 开始完整统计系统测试\n');
  console.log('=' * 50);
  
  // 1. 获取初始统计
  console.log('\n📈 第一步：获取初始统计数据');
  const initialStats = await getCurrentStats();
  
  // 2. 测试匿名用户生成
  console.log('\n📈 第二步：测试匿名用户生成');
  await testAnonymousGeneration();
  
  // 3. 测试登录用户生成
  console.log('\n📈 第三步：测试登录用户生成');
  await testLoggedUserGeneration();
  
  // 4. 获取更新后的统计
  console.log('\n📈 第四步：获取更新后的统计数据');
  const updatedStats = await getCurrentStats();
  
  // 5. 比较统计变化
  if (initialStats && updatedStats) {
    console.log('\n📊 统计变化对比:');
    const change = updatedStats.real_time_count - initialStats.real_time_count;
    console.log(`   初始总数: ${initialStats.real_time_count}`);
    console.log(`   更新总数: ${updatedStats.real_time_count}`);
    console.log(`   变化量: ${change > 0 ? '+' : ''}${change}`);
    
    const expectedChange = TEST_CONFIG.anonymousTestCount + 
                          (TEST_CONFIG.authToken ? TEST_CONFIG.loggedTestCount : 0);
    
    if (change === expectedChange) {
      console.log(`   ✅ 变化量符合预期 (${expectedChange})`);
    } else {
      console.log(`   ⚠️  变化量不符合预期 (期望: ${expectedChange}, 实际: ${change})`);
    }
  }
  
  // 6. 测试数据一致性
  console.log('\n📈 第五步：测试数据一致性');
  await testDataConsistency();
  
  // 7. 测试手动更新
  console.log('\n📈 第六步：测试手动统计更新');
  await testManualStatsUpdate();
  
  console.log('\n' + '=' * 50);
  console.log('🎉 测试完成！');
  
  // 8. 最终统计
  console.log('\n📈 最终统计数据:');
  await getCurrentStats();
}

/**
 * 错误处理
 */
process.on('unhandledRejection', (error) => {
  console.error('❌ 未处理的错误:', error);
  process.exit(1);
});

// 检查是否在Node.js环境中运行
if (typeof window === 'undefined') {
  // 动态导入 node-fetch
  import('node-fetch').then(({ default: fetch }) => {
    global.fetch = fetch;
    runTests().catch(console.error);
  }).catch(() => {
    console.error('❌ 请安装 node-fetch: npm install node-fetch');
    process.exit(1);
  });
} else {
  console.error('❌ 此脚本需要在Node.js环境中运行');
} 