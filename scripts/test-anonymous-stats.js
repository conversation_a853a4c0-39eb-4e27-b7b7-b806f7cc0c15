/**
 * 测试匿名用户生成功能和统计更新
 * 使用方法: node scripts/test-anonymous-stats.js
 */

async function testAnonymousStats() {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
  
  console.log('🧪 测试匿名用户生成功能...\n');

  try {
    // 1. 获取修改前的统计数据
    console.log('1️⃣ 获取当前统计数据...');
    const beforeResponse = await fetch(`${baseUrl}/api/stats/global`);
    const beforeData = await beforeResponse.json();
    
    console.log('📊 修改前统计:');
    console.log(`   存储的总数: ${beforeData.total_prompts_generated}`);
    console.log(`   实时总数: ${beforeData.real_time_count}`);
    console.log(`   数据一致性: ${beforeData.is_data_consistent ? '✅' : '❌'}`);
    
    if (beforeData.breakdown) {
      console.log(`   生成类型: ${beforeData.breakdown.generated}`);
    }

    // 2. 测试匿名用户生成
    console.log('\n2️⃣ 测试匿名用户生成...');
    const generateResponse = await fetch(`${baseUrl}/api/ai/simple-generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        input: '测试匿名用户统计功能 - ' + new Date().toISOString()
      })
    });

    if (!generateResponse.ok) {
      const errorData = await generateResponse.json();
      throw new Error(`生成失败: ${errorData.error}`);
    }

    const generateData = await generateResponse.json();
    console.log('✅ 生成成功');
    console.log(`   意图分析: ${generateData.data.intentAnalysis.substring(0, 50)}...`);
    console.log(`   生成提示词: ${generateData.data.generatedPrompt.substring(0, 50)}...`);

    // 3. 等待触发器执行
    console.log('\n3️⃣ 等待数据库更新...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 4. 获取修改后的统计数据
    console.log('4️⃣ 获取更新后的统计数据...');
    const afterResponse = await fetch(`${baseUrl}/api/stats/global`);
    const afterData = await afterResponse.json();
    
    console.log('📊 修改后统计:');
    console.log(`   存储的总数: ${afterData.total_prompts_generated}`);
    console.log(`   实时总数: ${afterData.real_time_count}`);
    console.log(`   数据一致性: ${afterData.is_data_consistent ? '✅' : '❌'}`);
    
    if (afterData.breakdown) {
      console.log(`   生成类型: ${afterData.breakdown.generated}`);
    }

    // 5. 验证统计是否增加
    console.log('\n5️⃣ 验证统计更新:');
    const realTimeIncrease = afterData.real_time_count - beforeData.real_time_count;
    const storedIncrease = afterData.total_prompts_generated - beforeData.total_prompts_generated;
    
    console.log(`   实时统计增加: ${realTimeIncrease}`);
    console.log(`   存储统计增加: ${storedIncrease}`);
    
    if (realTimeIncrease >= 1) {
      console.log('✅ 实时统计正确更新');
    } else {
      console.log('❌ 实时统计未更新');
    }
    
    if (afterData.is_data_consistent) {
      console.log('✅ 存储统计正确更新（数据一致）');
    } else {
      console.log('⚠️  存储统计可能未及时更新（数据不一致）');
    }

    // 6. 检查生成类型统计
    if (beforeData.breakdown && afterData.breakdown) {
      const generatedIncrease = afterData.breakdown.generated - beforeData.breakdown.generated;
      console.log(`   生成类型增加: ${generatedIncrease}`);
      
      if (generatedIncrease >= 1) {
        console.log('✅ 生成类型统计正确更新');
      } else {
        console.log('❌ 生成类型统计未更新');
      }
    }

    // 7. 总结
    console.log('\n📋 测试结果总结:');
    console.log('='.repeat(50));
    console.log(`✅ API 调用: 成功`);
    console.log(`✅ 提示词生成: 成功`);
    console.log(`${realTimeIncrease >= 1 ? '✅' : '❌'} 实时统计更新: ${realTimeIncrease >= 1 ? '成功' : '失败'}`);
    console.log(`${afterData.is_data_consistent ? '✅' : '⚠️'} 数据一致性: ${afterData.is_data_consistent ? '正常' : '待同步'}`);
    console.log('='.repeat(50));
    
    if (realTimeIncrease >= 1) {
      console.log('🎉 匿名用户生成统计功能正常工作！');
      console.log('💡 首页的【智能生成】现在会正确计入统计数据');
    } else {
      console.log('⚠️  功能可能需要进一步调试');
      console.log('建议: 检查数据库连接和匿名用户记录是否正确创建');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.log('\n🔧 故障排除建议:');
    console.log('1. 确保应用服务器正在运行');
    console.log('2. 检查 DEEPSEEK_API_KEY 环境变量是否设置');
    console.log('3. 确保在 Supabase 中执行了 scripts/add-anonymous-user.sql');
    console.log('4. 检查数据库连接是否正常');
  }
}

// 运行测试
testAnonymousStats().catch(console.error); 