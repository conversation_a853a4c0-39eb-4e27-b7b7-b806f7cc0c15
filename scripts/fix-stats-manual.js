/**
 * 手动修复统计数据的脚本
 * 使用 API 调用而不是直接数据库访问
 */

async function fixStats() {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
  
  console.log('🔧 开始修复统计数据...\n');

  try {
    // 1. 获取当前状态
    console.log('1️⃣ 获取当前统计状态...');
    const getResponse = await fetch(`${baseUrl}/api/stats/global`);
    const currentData = await getResponse.json();
    
    console.log('📊 当前数据:');
    console.log(`   存储的总数: ${currentData.total_prompts_generated}`);
    console.log(`   实时总数: ${currentData.real_time_count}`);
    console.log(`   数据一致性: ${currentData.is_data_consistent ? '✅' : '❌'}`);
    console.log(`   数据源: ${currentData.data_source}\n`);

    if (!currentData.is_data_consistent) {
      console.log('⚠️  检测到数据不一致，需要修复');
      
      // 2. 手动触发更新
      console.log('2️⃣ 触发手动更新...');
      const postResponse = await fetch(`${baseUrl}/api/stats/global`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (postResponse.ok) {
        const updateResult = await postResponse.json();
        console.log('✅ 更新成功:', updateResult.message);
        console.log(`   新的统计数据: ${updateResult.actual_count}`);
      } else {
        const errorData = await postResponse.json();
        console.log('❌ 更新失败:', errorData.error);
        
        if (postResponse.status === 401) {
          console.log('💡 提示: 需要登录才能手动更新统计数据');
          console.log('   请在浏览器中登录后再试，或等待触发器自动更新');
        }
      }
    } else {
      console.log('✅ 数据一致性正常，无需修复');
    }

    // 3. 再次检查状态
    console.log('\n3️⃣ 验证修复结果...');
    const verifyResponse = await fetch(`${baseUrl}/api/stats/global`);
    const verifyData = await verifyResponse.json();
    
    console.log('📊 修复后数据:');
    console.log(`   存储的总数: ${verifyData.total_prompts_generated}`);
    console.log(`   实时总数: ${verifyData.real_time_count}`);
    console.log(`   数据一致性: ${verifyData.is_data_consistent ? '✅' : '❌'}`);
    console.log(`   数据源: ${verifyData.data_source}`);
    
    if (verifyData.is_data_consistent) {
      console.log('\n🎉 统计数据修复完成！');
    } else {
      console.log('\n⚠️  数据仍然不一致，可能需要数据库级别的修复');
      console.log('建议执行以下 SQL 来手动修复:');
      console.log(`
UPDATE public.global_stats 
SET 
  total_prompts_generated = (SELECT COUNT(*) FROM public.prompts),
  last_updated = NOW()
WHERE id = 1;
      `);
    }

  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error.message);
  }
}

// 运行修复
fixStats().catch(console.error); 