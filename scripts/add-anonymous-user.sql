-- 添加匿名用户记录，用于统计未登录用户的提示词生成
-- 在 Supabase SQL 编辑器中执行此脚本

-- 1. 创建匿名用户记录
-- 使用固定的UUID作为匿名用户ID
INSERT INTO public.profiles (
  id,
  username,
  intent_model_name,
  generation_model_name,
  created_at,
  updated_at
) VALUES (
  '00000000-0000-0000-0000-000000000000'::uuid,
  'anonymous_user',
  'DeepSeek Chat',
  'DeepSeek Chat',
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  username = EXCLUDED.username,
  intent_model_name = EXCLUDED.intent_model_name,
  generation_model_name = EXCLUDED.generation_model_name,
  updated_at = NOW();

-- 2. 验证匿名用户记录
SELECT 
  id,
  username,
  intent_model_name,
  generation_model_name,
  created_at
FROM public.profiles 
WHERE id = '00000000-0000-0000-0000-000000000000'::uuid;

-- 3. 检查当前统计状态
SELECT 
  'global_stats' as source,
  total_prompts_generated,
  last_updated
FROM public.global_stats
WHERE id = 1

UNION ALL

SELECT 
  'prompts_count' as source,
  COUNT(*)::bigint as total_prompts_generated,
  NOW() as last_updated
FROM public.prompts;

-- 4. 显示匿名用户的提示词统计（如果有的话）
SELECT 
  type,
  COUNT(*) as count,
  MIN(created_at) as first_created,
  MAX(created_at) as last_created
FROM public.prompts 
WHERE user_id = '00000000-0000-0000-0000-000000000000'::uuid
GROUP BY type
ORDER BY count DESC; 