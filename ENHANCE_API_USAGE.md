# 智能增强API使用文档

## 功能概述

新的智能增强API实现了两步识别机制：

1. **第一步：理解用户需求**
   - 判断用户输入所属的领域/行业（如前端开发、金融、法律等）
   - 判断用户的核心意图（如排查bug、了解公司信息、探索解决方案等）

2. **第二步：增强用户问题**
   - 自动扩展用户原始模糊输入，输出三类问题表达：
   - **简洁版**：一句话快速提问，适合对话场景
   - **详细版**：结合技术背景/上下文，完整表达问题
   - **探索版**：列出用户可能真正想问的多个相关问题（3～5条）

## API接口

### 端点
```
POST /api/ai/enhance
```

### 请求参数
```json
{
  "input": "用户输入的问题或描述",
  "provider": "AI提供商名称",
  "model": "AI模型名称"
}
```

### 响应格式
```json
{
  "success": true,
  "data": {
    "original_input": "用户原始输入",
    "analysis": {
      "domain": "识别的领域",
      "intent": "核心意图",
      "confidence": 0.95,
      "reasoning": "分析依据"
    },
    "enhanced_questions": {
      "concise": "简洁版问题",
      "detailed": "详细版问题",
      "exploratory": [
        "探索问题1",
        "探索问题2",
        "探索问题3",
        "探索问题4",
        "探索问题5"
      ]
    },
    "disclaimer": "免责声明（如果是金融相关）",
    "provider": "实际使用的AI提供商",
    "model": "实际使用的AI模型"
  }
}
```

## 使用示例

### 示例1：前端开发问题

**输入：**
```json
{
  "input": "前端页面报错",
  "provider": "deepseek",
  "model": "deepseek-chat"
}
```

**输出：**
```json
{
  "success": true,
  "data": {
    "analysis": {
      "domain": "前端开发",
      "intent": "排查页面无法正常显示的问题",
      "confidence": 0.9,
      "reasoning": "用户提到前端页面和报错，明显是技术开发领域的问题排查需求"
    },
    "enhanced_questions": {
      "concise": "前端页面加载后白屏，如何排查？",
      "detailed": "Vue3 开发的单页应用首页白屏，控制台无报错，如何系统排查页面未渲染的原因？需考虑路由挂载、入口文件、资源加载和异步数据等。",
      "exploratory": [
        "是不是某个资源（如 JS 文件）没加载导致白屏？",
        "如何查看页面是否正确挂载了根组件？",
        "使用 Vue Router 的路由配置是否影响了页面显示？",
        "控制台没报错还能从哪些角度排查？",
        "有没有异步接口阻塞导致页面内容没渲染？"
      ]
    },
    "disclaimer": ""
  }
}
```

### 示例2：金融股票问题

**输入：**
```json
{
  "input": "远达环保相关",
  "provider": "deepseek",
  "model": "deepseek-chat"
}
```

**输出：**
```json
{
  "success": true,
  "data": {
    "analysis": {
      "domain": "金融 / 公司分析",
      "intent": "了解公司业务背景和投资价值",
      "confidence": 0.85,
      "reasoning": "用户提到具体公司名称，通常是想了解该公司的相关信息"
    },
    "enhanced_questions": {
      "concise": "远达环保是做什么的？发展前景怎么样？",
      "detailed": "我想了解远达环保这家公司的主营业务、最近的财报表现、行业地位以及是否值得投资。",
      "exploratory": [
        "远达环保的主营业务和核心收入来源是什么？",
        "最近几个季度的财务表现如何？是否盈利？",
        "在环保行业中，它的市场份额或地位怎样？",
        "是否有近期重大公告或股东变化？",
        "长期来看是否具备投资潜力？目前股价被高估还是低估？"
      ]
    },
    "disclaimer": "风险提示：以上内容仅供参考，不构成投资建议。投资有风险，入市需谨慎。"
  }
}
```

## 前端集成

### React/Next.js 使用示例

```typescript
import { useState } from 'react';

interface EnhancedResult {
  original_input: string;
  analysis: {
    domain: string;
    intent: string;
    confidence: number;
    reasoning: string;
  };
  enhanced_questions: {
    concise: string;
    detailed: string;
    exploratory: string[];
  };
  disclaimer: string;
  provider: string;
  model: string;
}

export function useEnhanceAPI() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<EnhancedResult | null>(null);

  const enhance = async (input: string, provider: string, model: string) => {
    setLoading(true);
    try {
      const response = await fetch('/api/ai/enhance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input,
          provider,
          model,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '增强失败');
      }

      const apiResult = await response.json();
      setResult(apiResult.data);
      return apiResult.data;
    } catch (error) {
      console.error('增强失败:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return { enhance, loading, result };
}
```

## 特殊功能

### 1. 领域自动识别
系统能自动识别以下领域：
- 技术开发（前端、后端、移动端等）
- 金融投资（股票、基金、公司分析等）
- 教育学习（编程、语言、技能等）
- 法律咨询
- 医疗健康
- 商业管理
- 其他专业领域

### 2. 意图智能分析
系统能识别用户的核心意图：
- 问题排查/调试
- 信息了解/咨询
- 学习指导/教程
- 投资分析/建议
- 解决方案/方法
- 比较评估/选择

### 3. 金融免责声明
当识别到金融相关问题时，系统会自动添加投资风险提示，确保合规性。

### 4. 多AI提供商支持
支持多种AI提供商：
- DeepSeek
- OpenAI
- Google Gemini
- OpenRouter
- 其他OpenAI兼容接口

## 错误处理

### 常见错误码
- `400`: 请求参数错误
- `401`: 用户未登录
- `404`: 配置未找到
- `500`: 服务器内部错误

### 错误响应格式
```json
{
  "error": "错误描述",
  "details": "详细错误信息"
}
```

## 性能优化

### 1. 缓存机制
- 相同输入的结果会被缓存
- 减少重复API调用

### 2. 容错机制
- JSON解析失败时提供默认结果
- 网络错误时的重试机制

### 3. 并发控制
- 防止重复提交
- 请求队列管理

## 最佳实践

1. **输入优化**
   - 使用简洁明确的描述
   - 避免过于复杂的句子
   - 包含关键信息词汇

2. **错误处理**
   - 始终处理API调用异常
   - 提供用户友好的错误提示
   - 实现重试机制

3. **用户体验**
   - 显示加载状态
   - 提供复制功能
   - 支持重新生成

4. **数据管理**
   - 保存用户历史记录
   - 支持结果导出
   - 提供搜索功能 