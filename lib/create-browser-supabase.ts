/**
 * Returns a Supabase client for the browser **only** if the required
 * NEXT_PUBLIC_SUPABASE_URL / NEXT_PUBLIC_SUPABASE_ANON_KEY env-vars exist.
 * Otherwise returns null so callers can gracefully degrade.
 */
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import type { SupabaseClient } from "@supabase/supabase-js"

export function getBrowserSupabase(): SupabaseClient<any, "public", any> | null {
  if (
    typeof process === "undefined" ||
    !process.env.NEXT_PUBLIC_SUPABASE_URL ||
    !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  ) {
    // No public keys → running in preview / development without Supabase
    return null
  }

  return createClientComponentClient<{
    public: any
  }>({
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
    supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  })
}
