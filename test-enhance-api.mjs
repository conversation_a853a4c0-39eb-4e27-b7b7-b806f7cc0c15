// 测试新的增强API功能
import fetch from 'node-fetch';

const testCases = [
  {
    name: "前端技术问题",
    input: "前端页面报错",
    expectedDomain: "前端开发",
    expectedIntentKeywords: ["解决", "错误", "问题"]
  },
  {
    name: "模糊的公司查询", 
    input: "远达环保",
    expectedDomain: "金融",
    expectedIntentKeywords: ["公司", "信息", "分析"]
  },
  {
    name: "学习相关问题",
    input: "如何学习Python",
    expectedDomain: "教育",
    expectedIntentKeywords: ["学习", "方法", "指导"]
  }
];

async function testEnhanceAPI() {
  console.log("🚀 开始测试新的问题增强API...\n");
  
  for (const testCase of testCases) {
    console.log(`\n=== 测试案例: ${testCase.name} ===`);
    console.log(`📝 输入: "${testCase.input}"`);
    
    try {
      const response = await fetch('http://localhost:3000/api/ai/enhance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: testCase.input,
          provider: "deepseek",
          model: "deepseek-chat"
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error(`❌ API调用失败: ${errorData.error}`);
        continue;
      }
      
      const result = await response.json();
      
      if (result.success) {
        console.log(`✅ 成功调用API`);
        console.log(`\n📊 意图分析结果:`);
        console.log(`🎯 核心实体: ${result.data.analysis.core_entity}`);
        console.log(`📍 识别领域: ${result.data.analysis.domain}`);
        console.log(`💡 核心意图: ${result.data.analysis.core_intent}`);
        console.log(`📋 隐含背景: ${result.data.analysis.problem_context}`);
        console.log(`📊 置信度: ${Math.round(result.data.analysis.confidence * 100)}%`);
        console.log(`🤔 分析依据: ${result.data.analysis.reasoning}`);
        
        console.log(`\n👨‍💼 专家身份: ${result.data.enhancement.expert_role}`);
        console.log(`\n🎯 启发性问题:`);
        console.log(`📝 简洁版: ${result.data.enhancement.enhanced_questions.concise}`);
        console.log(`📋 详细版: ${result.data.enhancement.enhanced_questions.detailed}`);
        console.log(`🔍 行动版:`);
        result.data.enhancement.enhanced_questions.exploratory.forEach((q, i) => {
          console.log(`   ${i + 1}. ${q}`);
        });
        console.log(`💡 专业建议: ${result.data.enhancement.professional_advice}`);
        
        if (result.data.disclaimer) {
          console.log(`⚠️  免责声明: ${result.data.disclaimer}`);
        }
        
        // 验证结果质量
        const domainMatch = result.data.analysis.domain.includes(testCase.expectedDomain.substring(0, 2));
        const intentMatch = testCase.expectedIntentKeywords.some(keyword => 
          result.data.analysis.core_intent.includes(keyword)
        );
        
        console.log(`\n✅ 验证结果:`);
        console.log(`- 领域匹配: ${domainMatch ? '✅' : '❌'}`);
        console.log(`- 意图匹配: ${intentMatch ? '✅' : '❌'}`);
        console.log(`- 问题质量: ${result.data.enhancement.enhanced_questions.concise.length > 10 ? '✅' : '❌'}`);
        
      } else {
        console.error(`❌ API返回失败: ${result.error}`);
      }
      
    } catch (error) {
      console.error(`❌ 测试失败: ${error.message}`);
    }
    
    console.log(`\n${'='.repeat(60)}`);
  }
  
  console.log("\n🎉 测试完成!");
}

// 运行测试
testEnhanceAPI(); 