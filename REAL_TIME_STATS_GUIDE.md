# 实时数据看板系统完整指南

## 🎯 系统概述

实时数据看板显示所有用户生成和增强提示词的真实总和，包含详细的统计分析和趋势展示。

## 📊 数据来源

### 提示词类型
- **enhanced**: 通过 `/api/ai/enhance` 增强的提示词
- **generated**: 通过 `/api/ai/generate` 和 `/api/ai/simple-generate` 生成的提示词

### 数据存储
所有提示词保存在 `prompts` 表中：
```sql
CREATE TABLE prompts (
  id UUID PRIMARY KEY,
  user_id UUID NOT NULL,
  source_text TEXT,      -- 原始输入
  result_text TEXT,      -- 生成/增强后的结果
  type TEXT,             -- 'enhanced' 或 'generated'
  created_at TIMESTAMPTZ
);
```

## 🔧 系统架构

### 1. 数据库层
- **prompts 表**: 存储所有用户的提示词
- **global_stats 表**: 缓存全局统计数据
- **触发器**: 自动更新统计数据

### 2. API 层
- **GET /api/stats/global**: 获取全局统计数据
- **POST /api/stats/global**: 手动更新统计数据

### 3. 前端组件
- **LiveStats**: 首页实时数据看板
- **StatsAdminPage**: 管理员统计管理页面

## 📈 统计数据结构

### API 返回格式
```json
{
  "total_prompts_generated": 1234,
  "last_updated": "2024-01-15T10:30:00Z",
  "real_time_count": 1234,
  "is_data_consistent": true,
  "data_source": "database",
  "breakdown": {
    "enhanced": 567,
    "generated": 667,
    "total": 1234
  },
  "recent_trend": [
    {
      "date": "2024-01-15",
      "total": 45,
      "enhanced": 20,
      "generated": 25
    }
  ],
  "growth_rate": 12.5
}
```

### 数据字段说明
- `total_prompts_generated`: 全局统计表中的总数
- `real_time_count`: 实时查询 prompts 表的总数
- `is_data_consistent`: 两个数据是否一致
- `breakdown`: 按类型分解的统计
- `recent_trend`: 最近7天的每日统计
- `growth_rate`: 日平均增长数

## 🚀 部署和配置

### 1. 数据库初始化
```bash
# 1. 执行基础数据库脚本
psql -f scripts/init-database.sql

# 2. 执行修复脚本（更新类型约束和触发器）
psql -f scripts/fix-database-types.sql
```

### 2. 环境变量配置
```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_key
```

### 3. 验证数据完整性
```bash
# 运行验证脚本
node scripts/verify-and-fix-stats.js
```

## 🔍 数据验证和修复

### 自动验证
系统会自动检查：
1. 统计表数据与实际数据的一致性
2. 触发器是否正常工作
3. API 端点是否正常响应

### 手动修复
```bash
# 1. 验证并修复数据
node scripts/verify-and-fix-stats.js

# 2. 或通过 API 手动更新
curl -X POST http://localhost:3000/api/stats/global
```

## 📱 前端显示

### 实时数据看板特性
- ✅ 实时数据刷新（每30秒）
- ✅ 数据来源指示器
- ✅ 一致性状态显示
- ✅ 按类型分解统计
- ✅ 增长趋势显示
- ✅ 响应式设计

### 状态指示器
- 🟢 **实时数据**: 从数据库获取的真实数据
- 🟡 **默认数据**: 数据库无记录时的默认值
- 🔴 **离线数据**: 网络错误时的备用数据
- ⚠️ **数据同步中**: 统计数据不一致时的警告

## 🛠 故障排除

### 常见问题

#### 1. 数据不一致
**现象**: 显示"数据正在同步中"
**原因**: 统计表与实际数据不匹配
**解决**: 运行 `node scripts/verify-and-fix-stats.js`

#### 2. 触发器失效
**现象**: 新生成的提示词不更新统计
**原因**: 数据库触发器可能被删除或失效
**解决**: 重新执行 `scripts/fix-database-types.sql`

#### 3. API 返回默认数据
**现象**: 始终显示默认数值
**原因**: 数据库连接问题或表为空
**解决**: 检查数据库连接和表结构

#### 4. 前端显示异常
**现象**: 数据不刷新或显示错误
**原因**: API 端点问题或网络错误
**解决**: 检查浏览器控制台和网络请求

### 调试步骤

1. **检查数据库**
   ```sql
   SELECT COUNT(*) FROM prompts;
   SELECT * FROM global_stats;
   ```

2. **测试 API**
   ```bash
   curl http://localhost:3000/api/stats/global
   ```

3. **查看日志**
   - 浏览器控制台
   - 服务器日志
   - 数据库日志

## 📊 性能优化

### 数据库优化
- 为 `prompts.type` 和 `prompts.created_at` 添加索引
- 定期清理过期数据
- 使用连接池优化数据库连接

### API 优化
- 实现缓存机制
- 使用并行查询
- 添加请求限流

### 前端优化
- 减少刷新频率
- 实现懒加载
- 添加加载状态

## 🔐 安全考虑

### 数据访问控制
- 全局统计数据可公开读取
- 用户个人数据受 RLS 保护
- 管理功能需要身份验证

### API 安全
- 实现请求限流
- 添加 CORS 配置
- 使用 HTTPS

## 📈 监控和分析

### 关键指标
- 总提示词数量
- 日增长率
- 用户活跃度
- API 响应时间
- 数据一致性状态

### 告警设置
- 数据不一致告警
- API 响应异常告警
- 数据库连接失败告警

## 🚀 未来改进

### 计划功能
- [ ] 实时 WebSocket 更新
- [ ] 更详细的统计分析
- [ ] 用户行为分析
- [ ] 数据导出功能
- [ ] 历史趋势图表

### 技术改进
- [ ] 使用 Redis 缓存
- [ ] 实现数据分片
- [ ] 添加 CDN 支持
- [ ] 优化查询性能

---

## 📞 技术支持

如果遇到问题，请按以下顺序排查：
1. 运行验证脚本
2. 检查数据库连接
3. 查看 API 响应
4. 检查前端控制台
5. 联系技术支持

**最后更新**: 2024年1月15日 