"use client"

import { useEffect, type ReactNode } from 'react'
import { useRouter } from 'next/navigation'
import { useUserStore } from '@/lib/user-store'
import { Loader2 } from 'lucide-react'

interface RouteGuardProps {
  children: ReactNode
  requireAuth?: boolean
}

export function RouteGuard({ children, requireAuth = false }: RouteGuardProps) {
  const { isAuthenticated, isLoading } = useUserStore()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && requireAuth && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, requireAuth, router])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#0A0A1A] flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-[#6f42c1]" />
      </div>
    )
  }

  if (requireAuth && !isAuthenticated) {
    return null
  }

  return <>{children}</>
} 