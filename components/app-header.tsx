"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User, Settings, LogOut, Bell } from "lucide-react"
import type { User as SupabaseUser } from "@supabase/supabase-js"
import { getBrowserSupabase } from "@/lib/create-browser-supabase"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { useUserStore } from "@/lib/user-store"

interface AppHeaderProps {
  user: SupabaseUser | null
}

export function AppHeader({ user }: AppHeaderProps) {
  if (!user) {
    return null
  }
  const supabase = getBrowserSupabase()
  const router = useRouter()
  const { toast } = useToast()
  const { logout } = useUserStore()

  const handleSignOut = async () => {
    if (supabase) {
      const { error } = await supabase.auth.signOut()
      if (error) {
        toast({
          title: "登出失败",
          description: error.message,
          variant: "destructive",
        })
        return
      }
    }
    
    logout()
    toast({
      title: "已登出",
      description: "您已成功登出",
    })
    router.push("/")
  }

  const getUserInitials = (email: string) => {
    return email.charAt(0).toUpperCase()
  }

  const getUserDisplayName = () => {
    return user.user_metadata?.username || user.email?.split("@")[0] || "User"
  }

  return (
    <header className="bg-gray-900/30 border-b border-gray-700 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-semibold text-white">{/* 这里可以根据当前页面动态显示标题 */}</h1>
        </div>

        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
            <Bell className="w-5 h-5" />
          </Button>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={user.user_metadata?.avatar_url || "/placeholder.svg"} />
                  <AvatarFallback className="bg-[#6f42c1] text-white">
                    {getUserInitials(user.email || "")}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 bg-gray-800 border-gray-700" align="end">
              <DropdownMenuLabel className="text-white">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium">{getUserDisplayName()}</p>
                  <p className="text-xs text-gray-400">{user.email}</p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-gray-700" />
              <DropdownMenuItem className="text-gray-300 hover:text-white hover:bg-gray-700">
                <User className="mr-2 h-4 w-4" />
                个人资料
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-gray-300 hover:text-white hover:bg-gray-700"
                onClick={() => router.push("/app/settings")}
              >
                <Settings className="mr-2 h-4 w-4" />
                设置
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-gray-700" />
              <DropdownMenuItem className="text-red-400 hover:text-red-300 hover:bg-gray-700" onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4" />
                登出
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}
