# 匿名用户统计功能实现指南

## 需求描述

用户希望首页的【智能生成】功能也计入统计数据，包括未登录用户的使用。

## 实现方案

### 方案概述
修改 `/api/ai/simple-generate` 接口，让未登录用户的生成也计入全局统计。

### 技术实现

#### 1. API 修改
已修改 `app/api/ai/simple-generate/route.ts`：

```javascript
// 更新统计数据（包括未登录用户）
if (result.generatedPrompt) {
  try {
    // 如果用户已登录，保存详细记录
    if (isLoggedIn) {
      const { error: insertError } = await supabase.from("prompts").insert({
        user_id: user.id,
        source_text: input,
        result_text: result.generatedPrompt,
        type: "generated",
      });
    }
    
    // 无论是否登录，都更新全局统计（使用服务端客户端）
    const serviceClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY || ""
    );
    
    // 直接更新全局统计数据
    const { data: currentStats } = await serviceClient
      .from("global_stats")
      .select("total_prompts_generated")
      .eq("id", 1)
      .single();
    
    const newCount = (currentStats?.total_prompts_generated || 0) + 1;
    
    const { error: manualUpdateError } = await serviceClient
      .from("global_stats")
      .upsert({
        id: 1,
        total_prompts_generated: newCount,
        last_updated: new Date().toISOString()
      });
  }
}
```

#### 2. 环境变量要求
需要在环境变量中设置：
- `NEXT_PUBLIC_SUPABASE_URL`: Supabase 项目 URL
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase 服务端密钥（用于绕过 RLS）

## 部署步骤

### 1. 设置环境变量
在项目根目录创建 `.env.local` 文件：
```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
DEEPSEEK_API_KEY=your_deepseek_api_key
```

### 2. 数据库准备（可选）
如果需要为匿名用户创建专门的记录，执行：
```sql
-- 在 Supabase SQL 编辑器中执行
INSERT INTO public.profiles (
  id,
  username,
  intent_model_name,
  generation_model_name,
  created_at,
  updated_at
) VALUES (
  '00000000-0000-0000-0000-000000000000'::uuid,
  'anonymous_user',
  'DeepSeek Chat',
  'DeepSeek Chat',
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  updated_at = NOW();
```

### 3. 修复现有统计数据
如果需要清理陈旧数据：
```sql
-- 重置全局统计
UPDATE public.global_stats 
SET 
  total_prompts_generated = (SELECT COUNT(*) FROM public.prompts),
  last_updated = NOW()
WHERE id = 1;
```

## 测试验证

### 1. 功能测试
```bash
# 测试匿名用户生成
curl -X POST http://localhost:3000/api/ai/simple-generate \
  -H "Content-Type: application/json" \
  -d '{"input": "测试匿名用户统计功能"}'

# 检查统计数据
curl -X GET http://localhost:3000/api/stats/global
```

### 2. 验证脚本
运行测试脚本：
```bash
node scripts/test-anonymous-stats.js
```

### 3. 预期结果
- API 调用成功返回生成的提示词
- 全局统计数据中的 `total_prompts_generated` 增加
- 前端显示的 `real_time_count` 保持准确

## 工作流程

### 用户操作流程
1. 用户在首页输入创意想法
2. 点击【智能生成】按钮
3. 系统调用 `/api/ai/simple-generate` 接口
4. 接口生成提示词并更新统计
5. 前端显示生成结果和更新后的统计数据

### 数据更新流程
1. **登录用户**：
   - 保存详细记录到 `prompts` 表
   - 触发器自动更新 `global_stats` 表
   
2. **匿名用户**：
   - 不保存详细记录（保护隐私）
   - 直接更新 `global_stats` 表

## 优势特点

### 1. 隐私保护
- 匿名用户的具体内容不保存到数据库
- 只统计使用次数，不记录个人信息

### 2. 数据准确性
- 所有用户的生成都计入统计
- 实时更新，无延迟

### 3. 性能优化
- 匿名用户操作更轻量
- 减少数据库存储压力

### 4. 用户体验
- 未登录用户也能完整使用功能
- 统计数据更真实反映使用情况

## 故障排除

### 1. 统计不更新
**可能原因**：
- 环境变量未设置
- 服务端密钥权限不足
- 数据库连接问题

**解决方案**：
- 检查 `.env.local` 文件
- 验证 Supabase 服务端密钥
- 查看服务器日志

### 2. API 调用失败
**可能原因**：
- DeepSeek API Key 未设置
- 网络连接问题
- API 限额超出

**解决方案**：
- 设置 `DEEPSEEK_API_KEY` 环境变量
- 检查网络连接
- 查看 API 使用量

### 3. 权限错误
**可能原因**：
- RLS 策略阻止操作
- 服务端密钥配置错误

**解决方案**：
- 使用服务端客户端绕过 RLS
- 验证密钥配置正确性

## 监控建议

### 1. 数据一致性监控
定期检查 `total_prompts_generated` 与实际数据是否一致

### 2. 性能监控
监控 API 响应时间和成功率

### 3. 使用统计
跟踪登录用户 vs 匿名用户的使用比例

## 后续优化

### 1. 批量更新
考虑批量更新统计数据以提高性能

### 2. 缓存机制
添加统计数据缓存减少数据库压力

### 3. 更细粒度统计
可以增加按时间段、类型等维度的统计

## 总结

通过修改 `simple-generate` API，现在首页的【智能生成】功能能够：
- ✅ 为所有用户（包括未登录）提供完整功能
- ✅ 准确统计所有用户的使用情况
- ✅ 保护匿名用户隐私
- ✅ 实时更新统计数据
- ✅ 在前端正确显示使用统计

这个实现既满足了统计需求，又保持了良好的用户体验和隐私保护。 