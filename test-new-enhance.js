// 测试新的启发性问题生成功能
const testCases = [
  "远达环保",
  "前端页面报错",
  "学习Python",
  "投资理财",
  "健身减肥"
];

async function testEnhanceAPI(input) {
  console.log(`\n=== 测试输入: "${input}" ===`);
  
  try {
    const response = await fetch('http://localhost:3000/api/ai/enhance', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        input: input,
        provider: 'deepseek',
        model: 'deepseek-chat'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ 请求成功');
      console.log('📊 分析结果:');
      console.log(`   - 识别实体: ${result.data.analysis.specific_entity}`);
      console.log(`   - 专业领域: ${result.data.analysis.domain}`);
      console.log(`   - 可能场景: ${result.data.analysis.likely_scenario}`);
      console.log(`   - 专业程度: ${result.data.analysis.professional_level}`);
      console.log(`   - 关键信息: ${result.data.analysis.key_details}`);
      console.log(`   - 置信度: ${Math.round(result.data.analysis.confidence * 100)}%`);
      
      console.log('\n💡 生成的专业问题:');
      result.data.enhancement.enhanced_questions.forEach((question, index) => {
        const titles = ['简洁版', '详细版', '分析版'];
        console.log(`\n${titles[index] || `第${index + 1}版`}:`);
        console.log(`   ${question}`);
      });
      
      if (result.data.enhancement.disclaimer) {
        console.log(`\n⚠️ 免责声明: ${result.data.enhancement.disclaimer}`);
      }
    } else {
      console.log('❌ 请求失败:', result.error);
    }
  } catch (error) {
    console.error('❌ 网络错误:', error.message);
  }
}

async function runAllTests() {
  console.log('🚀 开始测试新的专业问题生成功能...\n');
  
  for (const testCase of testCases) {
    await testEnhanceAPI(testCase);
    // 等待一下避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n🎉 所有测试完成！');
}

// 运行测试
runAllTests(); 