# 统计数据更新问题分析与解决方案

## 问题描述

API `/api/stats/global` 中的 `total_prompts_generated` 字段没有正确更新，导致前端显示的数据不准确。

## 问题分析

### 当前状态
通过 API 测试发现：
```json
{
  "total_prompts_generated": 46,    // 存储在 global_stats 表中的陈旧数据
  "real_time_count": 0,             // 实际从 prompts 表查询的数据
  "is_data_consistent": false,      // 数据不一致
  "data_source": "database"
}
```

### 根本原因
1. **数据库中存在陈旧数据**: `global_stats` 表中的 `total_prompts_generated` 值为 46，但实际 `prompts` 表中没有数据
2. **触发器可能未正确工作**: 当 `prompts` 表数据变化时，触发器没有正确更新 `global_stats` 表
3. **数据不同步**: 存储的统计数据与实际数据不匹配

## 解决方案

### 方案 1: SQL 直接修复（推荐）

在 Supabase 控制台的 SQL 编辑器中执行 `scripts/fix-stats-sql.sql`：

```sql
-- 手动修复统计数据
UPDATE public.global_stats 
SET 
  total_prompts_generated = (
    SELECT COUNT(*) 
    FROM public.prompts 
    WHERE type IN ('enhanced', 'generated', 'simple_generated')
  ),
  last_updated = NOW()
WHERE id = 1;
```

### 方案 2: API 手动更新

需要登录后调用：
```bash
curl -X POST http://localhost:3000/api/stats/global \
  -H "Content-Type: application/json"
```

### 方案 3: 重建触发器

确保触发器正确工作：
```sql
-- 重新创建触发器函数
CREATE OR REPLACE FUNCTION update_global_stats()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.global_stats 
  SET 
    total_prompts_generated = (
      SELECT COUNT(*) 
      FROM public.prompts 
      WHERE type IN ('enhanced', 'generated', 'simple_generated')
    ),
    last_updated = NOW()
  WHERE id = 1;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 重新创建触发器
DROP TRIGGER IF EXISTS update_stats_on_prompt_insert ON public.prompts;
CREATE TRIGGER update_stats_on_prompt_insert
  AFTER INSERT ON public.prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_global_stats();
```

## 前端改进

已经修改前端代码使用 `real_time_count` 而不是 `total_prompts_generated`：

```javascript
// 修改前
setTotalPrompts(data.total_prompts_generated || 1432144);

// 修改后
setTotalPrompts(data.real_time_count || 0);
```

这样即使 `total_prompts_generated` 不准确，前端也会显示实时的准确数据。

## 验证步骤

### 1. 执行修复脚本
```bash
node scripts/test-stats-after-fix.js
```

### 2. 检查 API 响应
```bash
curl -X GET http://localhost:3000/api/stats/global | jq .
```

### 3. 验证数据一致性
确保返回的数据中：
- `is_data_consistent: true`
- `total_prompts_generated` 等于 `real_time_count`

## 预防措施

### 1. 定期检查
创建定期检查脚本，监控数据一致性

### 2. 触发器监控
确保数据库触发器正常工作

### 3. API 改进
API 现在返回多个数据源：
- `total_prompts_generated`: 存储的统计数据
- `real_time_count`: 实时查询结果
- `is_data_consistent`: 一致性检查

### 4. 前端容错
前端优先使用 `real_time_count`，确保显示准确数据

## 测试用例

### 1. 数据一致性测试
```javascript
// 检查 total_prompts_generated 是否等于 real_time_count
const isConsistent = Math.abs(
  data.total_prompts_generated - data.real_time_count
) <= 1;
```

### 2. 触发器测试
插入测试数据，验证统计是否自动更新：
```sql
INSERT INTO public.prompts (user_id, source_text, result_text, type)
VALUES ('test-user-id', 'test', 'test prompt', 'generated');

-- 检查统计是否更新
SELECT * FROM public.global_stats WHERE id = 1;
```

## 故障排除

### 如果数据仍然不一致

1. **检查触发器状态**:
   ```sql
   SELECT * FROM information_schema.triggers 
   WHERE trigger_name LIKE '%stats%';
   ```

2. **手动重新计算**:
   ```sql
   UPDATE public.global_stats 
   SET total_prompts_generated = (SELECT COUNT(*) FROM public.prompts)
   WHERE id = 1;
   ```

3. **重建整个统计系统**:
   执行 `scripts/fix-database-types.sql`

### 如果触发器不工作

1. 检查权限设置
2. 重新创建触发器函数
3. 验证表结构完整性

## 总结

通过以上解决方案，`total_prompts_generated` 字段将能够正确更新，确保统计数据的准确性。前端改进使用 `real_time_count` 提供了额外的保障，即使存储的统计数据有问题，用户仍能看到准确的实时数据。 