// 测试提示词设计的有效性
const testCases = [
  {
    name: "前端技术问题",
    input: "前端页面报错",
    expectedDomain: "前端开发",
    expectedIntent: "解决页面错误问题"
  },
  {
    name: "模糊的公司查询", 
    input: "远达环保",
    expectedDomain: "金融投资",
    expectedIntent: "获取公司信息"
  },
  {
    name: "学习相关问题",
    input: "如何学习Python",
    expectedDomain: "教育培训",
    expectedIntent: "学习方法指导"
  }
];

// 第一步提示词
const domainAnalysisPrompt = `# 角色
你是一个专业的用户意图分析专家，擅长理解用户模糊、不完整的表达。

# 任务
用户经常不知道如何准确表达自己的需求，你需要透过他们模糊的表达，理解他们真正想要什么。

请分析用户输入，识别以下要素：
1. **核心实体** - 用户在说什么东西/事物
2. **问题领域** - 这属于哪个专业领域
3. **用户意图** - 用户真正想要达成什么目的
4. **隐含背景** - 用户可能处于什么情况或有什么未说出的需求

# 输出格式
严格按照以下JSON格式输出：
{
  "core_entity": "用户提到的核心事物",
  "domain": "所属专业领域",
  "core_intent": "用户的真实意图",
  "implied_context": "推测的背景情况",
  "confidence": 0.9,
  "reasoning": "分析推理过程"
}`;

// 第二步提示词
const questionEnhancePrompt = `# 角色
你是该领域的资深专家，擅长提出高质量、有启发性的问题，帮助用户从不同角度深入思考。

# 任务
基于用户的意图分析，生成三个层次的启发性问题，帮助用户明确自己真正想要探索的方向。

## 三个层次说明：
1. **简洁版** - 直击核心的精准问题，帮助用户快速定位重点
2. **详细版** - 包含专业背景和技术细节的深度问题，引导系统性思考
3. **行动版** - 提供具体的分析框架或解决路径，给出可操作的指导

# 输出格式
严格按照以下JSON格式输出：
{
  "expert_role": "该领域专家身份",
  "enhanced_questions": {
    "concise": "简洁版问题",
    "detailed": "详细版问题", 
    "actionable": "行动版问题"
  },
  "professional_advice": "专业建议",
  "disclaimer": "如果是金融领域则包含风险提示，否则为空字符串"
}`;

function testPromptDesign() {
  console.log("🧪 测试提示词设计的逻辑...\n");
  
  for (const testCase of testCases) {
    console.log(`=== 测试案例: ${testCase.name} ===`);
    console.log(`📝 用户输入: "${testCase.input}"`);
    
    // 模拟第一步分析
    console.log(`\n🔍 第一步分析 - 理解用户意图:`);
    console.log(`预期识别:`);
    console.log(`- 领域: ${testCase.expectedDomain}`);
    console.log(`- 意图: ${testCase.expectedIntent}`);
    
    // 模拟第二步问题生成
    console.log(`\n💡 第二步生成 - 启发性问题:`);
    console.log(`应该生成:`);
    console.log(`- 简洁版: 直击核心的精准问题`);
    console.log(`- 详细版: 包含专业背景的深度问题`);
    console.log(`- 行动版: 提供具体的解决路径`);
    
    console.log(`\n${'='.repeat(60)}`);
  }
  
  console.log("\n✅ 提示词设计测试完成!");
  console.log("\n📋 设计要点:");
  console.log("1. 第一步专注于理解用户的模糊表达");
  console.log("2. 第二步化身专家提供启发性问题");
  console.log("3. 三个层次的问题帮助用户深入思考");
  console.log("4. 解决'用户不知道该问什么'的痛点");
}

// 运行测试
testPromptDesign(); 