"use client";

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Canvas } from "@react-three/fiber";
import { Stars, OrbitControls } from "@react-three/drei";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Sparkles,
  Zap,
  Brain,
  Users,
  Star,
  ArrowRight,
  LogOut,
  Copy,
  CheckCircle,
  RefreshCw,
  Wand2,
  User,
  Play,
  Pause,
  Volume2,
  Maximize,
} from "lucide-react";
import Link from "next/link";
import { getBrowserSupabase } from "@/lib/create-browser-supabase";
import { useUserStore } from "@/lib/user-store";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import Image from "next/image";
import { useRef } from "react";

// Preloader Component
function Preloader({ onComplete }: { onComplete: () => void }) {
  useEffect(() => {
    const timer = setTimeout(onComplete, 2000);
    return () => clearTimeout(timer);
  }, [onComplete]);

  return (
    <motion.div
      className="fixed inset-0 z-50 bg-[#0A0A1A] flex items-center justify-center"
      exit={{ opacity: 0 }}
      transition={{ duration: 0.8 }}
    >
      <motion.div
        className="relative"
        initial={{ scale: 0.5, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 1, ease: "easeOut" }}
      >
        <motion.div
          className="w-32 h-32 border-2 border-[#6f42c1] rounded-full shadow-[0_0_50px_rgba(111,66,193,0.5)]"
          animate={{ rotate: 360 }}
          transition={{
            duration: 2,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
        />
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 1.5, repeat: Number.POSITIVE_INFINITY }}
        >
          <Sparkles className="w-12 h-12 text-[#00BFFF] drop-shadow-[0_0_20px_rgba(0,191,255,0.8)]" />
        </motion.div>
      </motion.div>
    </motion.div>
  );
}

// Enhanced 3D Background with Particle Effects
function StarField() {
  return (
    <div className="absolute inset-0">
      {/* Particle Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-[#1A1A3A] via-transparent to-[#0A0A1A]" />
        {/* Animated particles */}
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-[#00BFFF] rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.3, 1, 0.3],
              scale: [0.5, 1.5, 0.5],
            }}
            transition={{
              duration: 2 + Math.random() * 3,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>
      <Canvas className="absolute inset-0">
        <Stars
          radius={100}
          depth={50}
          count={5000}
          factor={4}
          saturation={0}
          fade
          speed={1}
        />
        <OrbitControls
          enableZoom={false}
          enablePan={false}
          enableRotate={true}
          autoRotate
          autoRotateSpeed={0.5}
        />
      </Canvas>
    </div>
  );
}

// Live Stats Component with Glassmorphism
function LiveStats() {
  const [totalPrompts, setTotalPrompts] = useState(1432144);
  const [loading, setLoading] = useState(true);
  const [dataSource, setDataSource] = useState<string>("");
  const [isDataConsistent, setIsDataConsistent] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<string>("");
  const [breakdown, setBreakdown] = useState<{enhanced: number, generated: number, total: number} | null>(null);
  const [growthRate, setGrowthRate] = useState<number>(0);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await fetch("/api/stats/global");
        const data = await response.json();

        if (response.ok) {
          setTotalPrompts(data.real_time_count || 0);
          setDataSource(data.data_source || "api");
          setIsDataConsistent(data.is_data_consistent !== false);
          setLastUpdated(data.last_updated || "");
          setBreakdown(data.breakdown || null);
          setGrowthRate(data.growth_rate || 0);
        } else {
          console.error("获取统计数据失败:", data.error);
          // 使用默认值
          setTotalPrompts(1432144);
          setDataSource("fallback");
        }
      } catch (error) {
        console.error("请求统计数据失败:", error);
        setTotalPrompts(1432144);
        setDataSource("fallback");
      } finally {
        setLoading(false);
      }
    };

    fetchStats();

    // 设置定时刷新（每30秒）
    const interval = setInterval(fetchStats, 30000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  return (
    <motion.div
      className="text-center py-16"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 2.5, duration: 0.8 }}
    >
      <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 max-w-md mx-auto shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.2)] transition-all duration-300">
        <h3 className="text-2xl font-bold text-white mb-4">实时数据看板</h3>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-8 h-8 text-[#6f42c1] animate-spin" />
          </div>
        ) : (
          <>
            <motion.div
              className="text-6xl font-bold bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(111,66,193,0.5)]"
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
            >
              {totalPrompts.toLocaleString()}
            </motion.div>
            <p className="text-gray-300 mt-2">提示词已生成</p>

            {/* 详细统计信息 */}
            {breakdown && (
              <div className="mt-4 grid grid-cols-2 gap-3 w-full max-w-xs">
                <div className="backdrop-blur-md bg-white/5 p-3 rounded-xl border border-white/10">
                  <div className="text-sm text-[#00BFFF] font-semibold">增强</div>
                  <div className="text-lg font-bold text-white">{breakdown.enhanced.toLocaleString()}</div>
                </div>
                <div className="backdrop-blur-md bg-white/5 p-3 rounded-xl border border-white/10">
                  <div className="text-sm text-[#6f42c1] font-semibold">生成</div>
                  <div className="text-lg font-bold text-white">{breakdown.generated.toLocaleString()}</div>
                </div>
              </div>
            )}

            {/* 增长趋势 */}
            {growthRate > 0 && (
              <div className="mt-3 flex items-center space-x-2">
                <div className="flex items-center space-x-1 text-sm text-green-400">
                  <span>📈</span>
                  <span>日均增长 {growthRate} 个</span>
                </div>
              </div>
            )}

            {/* 数据状态指示器 */}
            <div className="mt-4 flex flex-col items-center space-y-2">
              <div className="flex items-center space-x-2">
                <div
                  className={`w-2 h-2 rounded-full ${
                    dataSource === "database"
                      ? "bg-green-400"
                      : dataSource === "default"
                      ? "bg-yellow-400"
                      : "bg-red-400"
                  }`}
                />
                <span className="text-xs text-gray-400">
                  {dataSource === "database"
                    ? "实时数据"
                    : dataSource === "default"
                    ? "默认数据"
                    : "离线数据"}
                </span>
              </div>

              {!isDataConsistent && dataSource === "database" && (
                <div className="flex items-center space-x-1 text-xs text-yellow-400">
                  <span>⚠️</span>
                  <span>数据正在同步中</span>
                </div>
              )}

              {lastUpdated && (
                <div className="text-xs text-gray-500">
                  更新于: {new Date(lastUpdated).toLocaleTimeString()}
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </motion.div>
  );
}

// Video Demo Component
function VideoDemo() {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(true);

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const toggleFullscreen = () => {
    if (videoRef.current) {
      if (videoRef.current.requestFullscreen) {
        videoRef.current.requestFullscreen();
      }
    }
  };

  return (
    <motion.section
      id="video-demo"
      className="relative z-10 py-20 px-6"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 1.5, duration: 0.8 }}
    >
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.h2
            className="text-4xl font-bold mb-4 bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(111,66,193,0.5)]"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.7, duration: 0.8 }}
          >
            产品演示
          </motion.h2>
          <motion.p
            className="text-xl text-gray-300"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.9, duration: 0.8 }}
          >
            观看 PromptGenesis 如何将您的想法转化为专业提示词
          </motion.p>
        </div>

        {/* Video Container */}
        <motion.div
          className="relative group"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 2.1, duration: 0.8 }}
          whileHover={{ scale: 1.02 }}
        >
          <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-4 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.2)] transition-all duration-300">
            <div className="relative rounded-2xl overflow-hidden bg-black/20">
              <video
                ref={videoRef}
                className="w-full h-auto max-h-[600px] object-cover"
                muted={isMuted}
                loop
                playsInline
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
              >
                <source
                  src="https://zhangyanhua.oss-cn-beijing.aliyuncs.com/video/Capto_Capture%202025-07-05_11-53-34_%E4%B8%8B%E5%8D%88.mp4"
                  type="video/mp4"
                />
                您的浏览器不支持视频播放
              </video>

              {/* Video Controls Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between">
                  {/* Play/Pause Button */}
                  <div className="flex items-center space-x-3">
                    <Button
                      onClick={togglePlay}
                      size="sm"
                      className="bg-white/20 hover:bg-white/30 backdrop-blur-md border-white/20 text-white hover:scale-110 transition-all duration-300 rounded-full w-12 h-12 p-0"
                    >
                      {isPlaying ? (
                        <Pause className="w-5 h-5" />
                      ) : (
                        <Play className="w-5 h-5 ml-0.5" />
                      )}
                    </Button>

                    {/* Mute Button */}
                    <Button
                      onClick={toggleMute}
                      size="sm"
                      variant="ghost"
                      className="text-white hover:bg-white/20 backdrop-blur-md transition-all duration-300 rounded-full w-10 h-10 p-0"
                    >
                      <Volume2
                        className={`w-4 h-4 ${isMuted ? "opacity-50" : ""}`}
                      />
                    </Button>
                  </div>

                  {/* Fullscreen Button */}
                  <Button
                    onClick={toggleFullscreen}
                    size="sm"
                    variant="ghost"
                    className="text-white hover:bg-white/20 backdrop-blur-md transition-all duration-300 rounded-full w-10 h-10 p-0"
                  >
                    <Maximize className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Play Button Overlay (when paused) */}
              {!isPlaying && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                  <Button
                    onClick={togglePlay}
                    size="lg"
                    className="bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] hover:from-[#5a359a] hover:to-[#0099cc] text-white rounded-full w-20 h-20 p-0 shadow-[0_8px_32px_rgba(111,66,193,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.5)] hover:scale-110 transition-all duration-300"
                  >
                    <Play className="w-8 h-8 ml-1" />
                  </Button>
                </div>
              )}
            </div>

            {/* Video Description */}
            <div className="mt-6 text-center">
              <div className="flex flex-wrap justify-center gap-2 mb-4">
                <div className="px-3 py-1 bg-[#00BFFF]/20 text-[#00BFFF] rounded-full text-sm border border-[#00BFFF]/30 backdrop-blur-md shadow-[0_0_10px_rgba(0,191,255,0.3)]">
                  实时演示
                </div>
                <div className="px-3 py-1 bg-[#6f42c1]/20 text-[#6f42c1] rounded-full text-sm border border-[#6f42c1]/30 backdrop-blur-md shadow-[0_0_10px_rgba(111,66,193,0.3)]">
                  完整流程
                </div>
                <div className="px-3 py-1 bg-[#ff007f]/20 text-[#ff007f] rounded-full text-sm border border-[#ff007f]/30 backdrop-blur-md shadow-[0_0_10px_rgba(255,0,127,0.3)]">
                  AI 驱动
                </div>
              </div>
              <p className="text-gray-300 leading-relaxed">
                从简单想法到专业提示词，体验 PromptGenesis 的完整工作流程
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
}

// Enhanced Quick Generate Component
function QuickGenerate() {
  const [inputIdea, setInputIdea] = useState("");
  const [generatedPrompt, setGeneratedPrompt] = useState("");
  const [intentAnalysis, setIntentAnalysis] = useState("");
  const [loading, setLoading] = useState(false);
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  const handleGenerate = async () => {
    if (!inputIdea.trim()) {
      toast({
        title: "请输入创意想法",
        description: "请先输入一个简单的创意想法",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      const response = await fetch("/api/ai/simple-generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          input: inputIdea,
        }),
      });

      if (!response.ok) {
        throw new Error("生成失败");
      }

      const result = await response.json();
      const intentAnalysis = result.data?.intentAnalysis || "";
      const generatedPrompt = result.data?.generatedPrompt || "";

      setIntentAnalysis(intentAnalysis);
      setGeneratedPrompt(generatedPrompt);

      toast({
        title: "生成完成！",
        description: "已成功生成专业提示词",
      });
    } catch (error) {
      console.error("生成失败:", error);
      toast({
        title: "生成失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(generatedPrompt);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      toast({
        title: "已复制",
        description: "提示词已复制到剪贴板",
      });
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      });
    }
  };

  return (
    <motion.section
      className="relative z-10 py-20 px-6"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 3.0, duration: 1 }}
    >
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.h2
            className="text-4xl font-bold mb-4 bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(111,66,193,0.5)]"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 3.2, duration: 0.8 }}
          >
            智能提示词生成器
          </motion.h2>
          <motion.p
            className="text-xl text-gray-300"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 3.4, duration: 0.8 }}
          >
            输入简单想法，获得专业级提示词
          </motion.p>
        </div>

        {/* Input Section */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 3.6, duration: 0.8 }}
        >
          <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(0,191,255,0.2)] transition-all duration-300">
            <div className="space-y-4">
              <div className="relative">
                <textarea
                  placeholder="例如：渤海汽车年报、如何学习AI相关知识、制作产品使用说明书..."
                  value={inputIdea}
                  onChange={(e) => setInputIdea(e.target.value)}
                  className="w-full min-h-[120px] bg-white/5 border border-white/10 text-white placeholder-gray-400 focus:border-[#00BFFF] focus:shadow-[0_0_20px_rgba(0,191,255,0.3)] text-lg p-4 rounded-xl resize-none focus:outline-none backdrop-blur-md transition-all duration-300"
                  onKeyPress={(e) =>
                    e.key === "Enter" &&
                    e.ctrlKey &&
                    !loading &&
                    handleGenerate()
                  }
                />
                <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                  {inputIdea.length} 字符
                </div>
              </div>
              <div className="flex justify-center">
                <Button
                  onClick={handleGenerate}
                  disabled={loading || !inputIdea.trim()}
                  size="lg"
                  className="bg-gradient-to-r from-[#00BFFF] to-[#6f42c1] hover:from-[#0099cc] hover:to-[#5a359a] px-8 py-3 text-lg font-semibold rounded-full shadow-[0_8px_32px_rgba(0,191,255,0.3)] hover:shadow-[0_12px_48px_rgba(0,191,255,0.5)] hover:scale-105 transition-all duration-300"
                >
                  {loading ? (
                    <>
                      <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
                      生成中...
                    </>
                  ) : (
                    <>
                      <Brain className="w-5 h-5 mr-2" />
                      智能生成 ✨
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Intent Analysis */}
        {intentAnalysis && (
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="backdrop-blur-xl bg-gradient-to-r from-[#00BFFF]/10 to-[#6f42c1]/10 border border-[#00BFFF]/30 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,191,255,0.2)]">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#00BFFF] to-[#6f42c1] flex items-center justify-center shadow-[0_0_20px_rgba(0,191,255,0.5)]">
                  <Wand2 className="w-4 h-4 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-[#00BFFF] drop-shadow-[0_0_10px_rgba(0,191,255,0.5)]">
                  意图理解
                </h3>
              </div>
              <div className="backdrop-blur-md bg-white/5 p-4 rounded-xl border-l-4 border-[#00BFFF]">
                <div className="text-sm text-gray-300 whitespace-pre-wrap font-sans leading-relaxed">
                  {intentAnalysis}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Generated Result */}
        {generatedPrompt && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.2)] transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] flex items-center justify-center shadow-[0_0_20px_rgba(111,66,193,0.5)]">
                    <Brain className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-[#6f42c1] drop-shadow-[0_0_10px_rgba(111,66,193,0.5)]">
                    专业提示词
                  </h3>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopy}
                  className="border-white/20 text-gray-300 hover:bg-white/10 bg-white/5 backdrop-blur-md hover:shadow-[0_0_20px_rgba(255,255,255,0.2)] transition-all duration-300"
                >
                  {copied ? (
                    <CheckCircle className="w-4 h-4 mr-2 text-green-400" />
                  ) : (
                    <Copy className="w-4 h-4 mr-2" />
                  )}
                  {copied ? "已复制" : "复制"}
                </Button>
              </div>
              <div className="backdrop-blur-md bg-white/5 p-6 rounded-xl border-l-4 border-[#6f42c1]">
                <div className="text-gray-300 leading-relaxed whitespace-pre-wrap font-sans">
                  {generatedPrompt}
                </div>
              </div>
              <div className="mt-4 flex flex-wrap gap-2">
                <div className="px-3 py-1 bg-[#00BFFF]/20 text-[#00BFFF] rounded-full text-sm border border-[#00BFFF]/30 backdrop-blur-md shadow-[0_0_10px_rgba(0,191,255,0.3)]">
                  DeepSeek 驱动
                </div>
                <div className="px-3 py-1 bg-[#6f42c1]/20 text-[#6f42c1] rounded-full text-sm border border-[#6f42c1]/30 backdrop-blur-md shadow-[0_0_10px_rgba(111,66,193,0.3)]">
                  双阶段生成
                </div>
                <div className="px-3 py-1 bg-[#ff007f]/20 text-[#ff007f] rounded-full text-sm border border-[#ff007f]/30 backdrop-blur-md shadow-[0_0_10px_rgba(255,0,127,0.3)]">
                  专业级输出
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </motion.section>
  );
}

export default function HomePage() {
  const [showPreloader, setShowPreloader] = useState(true);
  const [currentSlogan, setCurrentSlogan] = useState(0);
  const [typingText, setTypingText] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const { isAuthenticated, user, logout } = useUserStore();
  const router = useRouter();
  const supabase = getBrowserSupabase();

  const slogans = [
    "重塑想象，定义边界",
    "Where Ideas Transcend",
    "AI驱动的创意革命",
  ];
  const typingTexts = [
    "> 灵感，一键升维",
    "> 赋予AI你的思想深度",
    "> 重新定义创意边界",
    "> 从模糊想法到精准表达",
  ];

  const handleLogout = async () => {
    if (supabase) {
      await supabase.auth.signOut();
    }
    logout();
    router.refresh();
  };

  const scrollToVideo = () => {
    const videoSection = document.getElementById("video-demo");
    if (videoSection) {
      videoSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  // Typing effect
  // useEffect(() => {
  //   if (!showPreloader) {
  //     let currentIndex = 0;
  //     const typeText = () => {
  //       setIsTyping(true);
  //       const text = typingTexts[currentIndex];
  //       let charIndex = 0;

  //       const typeInterval = setInterval(() => {
  //         if (charIndex < text.length) {
  //           setTypingText(text.substring(0, charIndex + 1));
  //           charIndex++;
  //         } else {
  //           clearInterval(typeInterval);
  //           setTimeout(() => {
  //             setIsTyping(false);
  //             currentIndex = (currentIndex + 1) % typingTexts.length;
  //             setTimeout(typeText, 1000);
  //           }, 7000);
  //         }
  //       }, 700);
  //     };

  //     typeText();
  //   }
  // }, [showPreloader, typingTexts]);

  useEffect(() => {
    if (!showPreloader) {
      const interval = setInterval(() => {
        setCurrentSlogan((prev) => (prev + 1) % slogans.length);
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [showPreloader, slogans.length]);

  return (
    <>
      <AnimatePresence>
        {showPreloader && (
          <Preloader onComplete={() => setShowPreloader(false)} />
        )}
      </AnimatePresence>

      {!showPreloader && (
        <div className="min-h-screen bg-[#0A0A1A] text-white overflow-hidden">
          {/* Enhanced 3D Background */}
          <div className="fixed inset-0 z-0">
            <StarField />
          </div>

          {/* Enhanced Navigation with Glassmorphism */}
          <motion.nav
            className="relative z-10 "
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="flex justify-between items-center p-6">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] flex items-center justify-center shadow-[0_0_20px_rgba(111,66,193,0.5)]">
                  <Sparkles className="w-4 h-4 text-white" />
                </div>
                <span className="text-2xl font-bold drop-shadow-[0_0_10px_rgba(255,255,255,0.5)]">
                  PromptGenesis
                </span>
              </div>
              <div className="flex items-center space-x-4">
                {isAuthenticated ? (
                  <>
                    <Link href="/app">
                      <Button
                        variant="ghost"
                        className="text-white hover:text-[#00BFFF] hover:bg-white/10 backdrop-blur-md transition-all duration-300"
                      >
                        进入应用
                      </Button>
                    </Link>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-300">
                        欢迎, {user?.email?.split("@")[0]}
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleLogout}
                        className="text-white hover:text-[#ff007f] hover:bg-white/10 backdrop-blur-md transition-all duration-300"
                      >
                        <LogOut className="w-4 h-4" />
                      </Button>
                    </div>
                  </>
                ) : (
                  <>
                    <Link href="/login">
                      <Button
                        variant="ghost"
                        className="text-white hover:text-[#00BFFF] hover:bg-white/10 backdrop-blur-md transition-all duration-300"
                      >
                        登录
                      </Button>
                    </Link>
                    <Link href="/register">
                      <Button className="bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] hover:from-[#5a359a] hover:to-[#0099cc] shadow-[0_8px_32px_rgba(111,66,193,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.5)] hover:scale-105 transition-all duration-300">
                        开启免费增强
                      </Button>
                    </Link>
                  </>
                )}
              </div>
            </div>
          </motion.nav>

          {/* Enhanced Hero Section */}
          <motion.section
            className="relative z-10 text-center py-20 px-6"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3, duration: 1 }}
          >
            <motion.h1
              className="text-6xl md:text-8xl font-bold mb-8 drop-shadow-[0_0_30px_rgba(111,66,193,0.5)]"
              style={{ fontFamily: "Monument Extended, sans-serif" }}
            >
              <span className="bg-gradient-to-r from-[#6f42c1] via-[#00BFFF] to-[#ff007f] bg-clip-text text-transparent">
                PromptGenesis
              </span>
            </motion.h1>

            <AnimatePresence mode="wait">
              <motion.p
                key={currentSlogan}
                className="text-2xl md:text-3xl text-gray-300 mb-4 drop-shadow-[0_0_20px_rgba(255,255,255,0.3)]"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
              >
                {slogans[currentSlogan]}
              </motion.p>
            </AnimatePresence>

            {/* Typing Effect */}
            <motion.div
              className="text-lg md:text-xl text-[#00BFFF] mb-12 font-mono h-8 flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1, duration: 0.5 }}
            >
              <span className="drop-shadow-[0_0_15px_rgba(0,191,255,0.5)]">
                {typingText}
                <motion.span
                  className="ml-1 text-[#00BFFF]"
                  animate={{
                    opacity: isTyping ? [1, 0.3, 1] : [1, 0, 1],
                  }}
                  transition={{
                    duration: isTyping ? 0.8 : 1.2,
                    repeat: Number.POSITIVE_INFINITY,
                    ease: "easeInOut",
                  }}
                >
                  |
                </motion.span>
              </span>
            </motion.div>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
            >
              <Link href={isAuthenticated ? "/app" : "/register"}>
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-[#ff007f] to-[#6f42c1] hover:from-[#e6006b] hover:to-[#5a359a] text-white px-8 py-4 text-lg font-semibold rounded-full shadow-[0_8px_32px_rgba(255,0,127,0.3)] hover:shadow-[0_12px_48px_rgba(255,0,127,0.5)] hover:scale-105 transition-all duration-300 group"
                >
                  {isAuthenticated ? "进入应用" : "免费开启增强之旅"}
                  <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Button>
              </Link>
              {isAuthenticated ? (
                <Link href="/app/generate">
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-[#6f42c1] text-[#6f42c1] hover:bg-[#6f42c1]/20 hover:text-white hover:shadow-[0_0_20px_rgba(111,66,193,0.5)] px-8 py-4 text-lg rounded-full bg-white/5 backdrop-blur-md border-white/20 transition-all duration-300"
                  >
                    开始生成
                  </Button>
                </Link>
              ) : (
                <Button
                  variant="outline"
                  size="lg"
                  onClick={scrollToVideo}
                  className="border-[#6f42c1] text-[#6f42c1] hover:bg-[#6f42c1]/20 hover:text-white hover:shadow-[0_0_20px_rgba(111,66,193,0.5)] px-8 py-4 text-lg rounded-full bg-white/5 backdrop-blur-md border-white/20 transition-all duration-300"
                >
                  观看演示
                </Button>
              )}
            </motion.div>
          </motion.section>

          {/* Video Demo Section */}
          <VideoDemo />

          {/* Live Stats Dashboard */}
          <LiveStats />

          {/* Quick Generate Section */}
          <QuickGenerate />

          {/* Enhanced Features Section */}
          <motion.section
            className="relative z-10 py-20 px-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 4.0, duration: 1 }}
          >
            <div className="max-w-6xl mx-auto">
              <h2 className="text-4xl font-bold text-center mb-16 bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(111,66,193,0.5)]">
                核心功能
              </h2>
              <div className="grid md:grid-cols-3 gap-8">
                {[
                  {
                    icon: <Zap className="w-8 h-8" />,
                    title: "智能增强",
                    description: "将简单想法转化为专业级提示词",
                    color: "from-[#6f42c1] to-[#8b5cf6]",
                    hoverColor:
                      "hover:shadow-[0_12px_48px_rgba(111,66,193,0.3)]",
                  },
                  {
                    icon: <Brain className="w-8 h-8" />,
                    title: "提示词生成",
                    description: "基于意图理解生成完美提示词",
                    color: "from-[#00BFFF] to-[#0ea5e9]",
                    hoverColor:
                      "hover:shadow-[0_12px_48px_rgba(0,191,255,0.3)]",
                  },
                  {
                    icon: <Users className="w-8 h-8" />,
                    title: "个人灵感库",
                    description: "保存和管理你的所有创意成果",
                    color: "from-[#ff007f] to-[#ec4899]",
                    hoverColor:
                      "hover:shadow-[0_12px_48px_rgba(255,0,127,0.3)]",
                  },
                ].map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 4.2 + index * 0.2, duration: 0.8 }}
                    whileHover={{
                      rotateY: 5,
                      rotateX: 5,
                      scale: 1.05,
                      transition: { duration: 0.3 },
                    }}
                    style={{ perspective: "1000px" }}
                  >
                    <div
                      className={`backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] ${feature.hoverColor} transition-all duration-300 group cursor-pointer`}
                    >
                      <div
                        className={`w-16 h-16 rounded-full bg-gradient-to-r ${feature.color} flex items-center justify-center mb-4 shadow-[0_0_20px_rgba(111,66,193,0.5)] group-hover:scale-110 group-hover:shadow-[0_0_30px_rgba(111,66,193,0.8)] transition-all duration-300`}
                      >
                        {feature.icon}
                      </div>
                      <h3 className="text-xl font-semibold mb-2 text-white group-hover:text-[#00BFFF] transition-colors duration-300">
                        {feature.title}
                      </h3>
                      <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                        {feature.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.section>

          {/* Enhanced User Reviews with Avatars */}
          <motion.section
            className="relative z-10 py-20 px-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 5.0, duration: 1 }}
          >
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-4xl font-bold mb-16 bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(111,66,193,0.5)]">
                用户评价
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                {[
                  {
                    name: "张设计师",
                    role: "UI/UX Designer",
                    content:
                      "PromptGenesis彻底改变了我的创作流程，从模糊想法到精确提示词只需几秒钟。",
                    rating: 5,
                    avatar: "/placeholder-user.jpg",
                    color: "from-[#6f42c1] to-[#00BFFF]",
                  },
                  {
                    name: "李开发者",
                    role: "Full Stack Developer",
                    content:
                      "作为开发者，我需要快速生成各种场景的提示词，这个工具简直是神器！",
                    rating: 5,
                    avatar: "/placeholder-user.jpg",
                    color: "from-[#00BFFF] to-[#ff007f]",
                  },
                ].map((review, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 5.2 + index * 0.3, duration: 0.8 }}
                    whileHover={{
                      rotateY: 10,
                      scale: 1.05,
                      transition: { duration: 0.3 },
                    }}
                    style={{ perspective: "1000px" }}
                  >
                    <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.2)] transition-all duration-300 group cursor-pointer">
                      {/* Avatar */}
                      <div className="flex justify-center mb-4">
                        <div
                          className={`w-16 h-16 rounded-full bg-gradient-to-r ${review.color} p-1 shadow-[0_0_20px_rgba(111,66,193,0.5)] group-hover:shadow-[0_0_30px_rgba(111,66,193,0.8)] transition-all duration-300`}
                        >
                          <div className="w-full h-full rounded-full bg-white/10 flex items-center justify-center backdrop-blur-md">
                            <User className="w-8 h-8 text-white" />
                          </div>
                        </div>
                      </div>

                      {/* Stars */}
                      <div className="flex justify-center mb-4">
                        {[...Array(review.rating)].map((_, i) => (
                          <Star
                            key={i}
                            className="w-5 h-5 text-yellow-400 fill-current drop-shadow-[0_0_10px_rgba(255,193,7,0.5)]"
                          />
                        ))}
                      </div>

                      {/* Review Content */}
                      <p className="text-gray-300 mb-4 italic leading-relaxed group-hover:text-white transition-colors duration-300">
                        "{review.content}"
                      </p>

                      {/* User Info */}
                      <div className="border-t border-white/10 pt-4">
                        <p className="font-semibold text-white group-hover:text-[#00BFFF] transition-colors duration-300">
                          {review.name}
                        </p>
                        <p className="text-sm text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                          {review.role}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.section>

          {/* Enhanced CTA Section */}
          <motion.section
            className="relative z-10 py-20 px-6 text-center"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 6.0, duration: 1 }}
          >
            <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-12 max-w-4xl mx-auto shadow-[0_8px_32px_rgba(0,0,0,0.3)]">
              <h2 className="text-4xl font-bold mb-8 bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(111,66,193,0.5)]">
                {isAuthenticated
                  ? "欢迎回来，继续创造精彩！"
                  : "准备好开始创造了吗？"}
              </h2>
              <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto leading-relaxed">
                {isAuthenticated
                  ? "您的创意工具箱已准备就绪，开始您的下一个创作项目吧"
                  : "加入数千名创作者的行列，体验AI驱动的提示词革命"}
              </p>
              <Link href={isAuthenticated ? "/app" : "/register"}>
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] hover:from-[#5a359a] hover:to-[#0099cc] text-white px-12 py-6 text-xl font-semibold rounded-full shadow-[0_8px_32px_rgba(111,66,193,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.5)] hover:scale-105 transition-all duration-300 group"
                >
                  {isAuthenticated ? "进入应用体验" : "立即开始免费使用"}
                  <Sparkles className="ml-2 w-6 h-6 group-hover:rotate-12 transition-transform duration-300 drop-shadow-[0_0_10px_rgba(255,255,255,0.5)]" />
                </Button>
              </Link>
            </div>
          </motion.section>

          {/* Enhanced Footer */}
          <footer className="relative z-10 border-t border-white/10 backdrop-blur-xl bg-white/5 py-8 px-6">
            <div className="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-center">
              <div className="flex items-center space-x-2 mb-4 md:mb-0">
                <div className="w-6 h-6 rounded-full bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] flex items-center justify-center shadow-[0_0_15px_rgba(111,66,193,0.5)]">
                  <Sparkles className="w-3 h-3 text-white" />
                </div>
                <span className="text-lg font-semibold drop-shadow-[0_0_10px_rgba(255,255,255,0.5)]">
                  PromptGenesis
                </span>
              </div>
              <div className="flex space-x-6 text-gray-400">
                <a
                  href="#"
                  className="hover:text-white hover:drop-shadow-[0_0_10px_rgba(255,255,255,0.5)] transition-all duration-300"
                >
                  隐私政策
                </a>
                <a
                  href="#"
                  className="hover:text-white hover:drop-shadow-[0_0_10px_rgba(255,255,255,0.5)] transition-all duration-300"
                >
                  服务条款
                </a>
                <a
                  href="#"
                  className="hover:text-white hover:drop-shadow-[0_0_10px_rgba(255,255,255,0.5)] transition-all duration-300"
                >
                  联系我们
                </a>
              </div>
            </div>
          </footer>
        </div>
      )}
    </>
  );
}
