"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Sparkles, Mail, Lock, Github } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { getBrowserSupabase } from "@/lib/create-browser-supabase"
import { useToast } from "@/hooks/use-toast"
import { useUserStore } from "@/lib/user-store"

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [resendingEmail, setResendingEmail] = useState(false)
  const [showResendButton, setShowResendButton] = useState(false)
  const router = useRouter()
  const supabase = getBrowserSupabase()
  const { toast } = useToast()
  const { setUser } = useUserStore()

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    if (!supabase) {
      toast({
        title: "尚未配置 Supabase",
        description: "请先设置 NEXT_PUBLIC_SUPABASE_URL 与 NEXT_PUBLIC_SUPABASE_ANON_KEY",
        variant: "destructive",
      })
      return
    }

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        let errorMessage = error.message
        let errorTitle = "登录失败"
        
        // 针对邮箱未验证的特殊处理
        if (error.message.includes("Email not confirmed") || error.message.includes("email_not_confirmed")) {
          errorTitle = "邮箱未验证"
          errorMessage = "请先验证您的邮箱地址。请检查您的邮箱（包括垃圾邮件文件夹）并点击验证链接。"
          setShowResendButton(true)
        }
        
        toast({
          title: errorTitle,
          description: errorMessage,
          variant: "destructive",
        })
      } else {
        // 更新用户状态
        setUser(data.user)
        toast({
          title: "登录成功",
          description: "欢迎回来！",
        })
        router.push("/app")
      }
    } catch (error) {
      toast({
        title: "登录失败",
        description: "发生未知错误，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleLogin = async () => {
    if (!supabase) {
      toast({
        title: "尚未配置 Supabase",
        description: "请先设置 NEXT_PUBLIC_SUPABASE_URL 与 NEXT_PUBLIC_SUPABASE_ANON_KEY",
        variant: "destructive",
      })
      return
    }

    const { error } = await supabase.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo: `${window.location.origin}/app`,
      },
    })

    if (error) {
      toast({
        title: "登录失败",
        description: error.message,
        variant: "destructive",
      })
    }
  }

  const handleGithubLogin = async () => {
    if (!supabase) {
      toast({
        title: "尚未配置 Supabase",
        description: "请先设置 NEXT_PUBLIC_SUPABASE_URL 与 NEXT_PUBLIC_SUPABASE_ANON_KEY",
        variant: "destructive",
      })
      return
    }

    const { error } = await supabase.auth.signInWithOAuth({
      provider: "github",
      options: {
        redirectTo: `${window.location.origin}/app`,
      },
    })

    if (error) {
      toast({
        title: "登录失败",
        description: error.message,
        variant: "destructive",
      })
    }
  }

  const handleResendVerification = async () => {
    if (!supabase || !email) {
      toast({
        title: "请先输入邮箱",
        description: "请在邮箱字段中输入您的邮箱地址",
        variant: "destructive",
      })
      return
    }

    setResendingEmail(true)

    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      })

      if (error) {
        toast({
          title: "重新发送失败",
          description: error.message,
          variant: "destructive",
        })
      } else {
        toast({
          title: "验证邮件已发送",
          description: "请查看您的邮箱（包括垃圾邮件文件夹）",
        })
        setShowResendButton(false)
      }
    } catch (error) {
      toast({
        title: "重新发送失败",
        description: "发生未知错误，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setResendingEmail(false)
    }
  }

  return (
    <div className="min-h-screen bg-[#0A0A1A] flex items-center justify-center p-6">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#6f42c1]/10 via-transparent to-[#00BFFF]/10" />

      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8 }}
        className="w-full max-w-md relative z-10"
      >
        {/* Logo */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2">
            <Sparkles className="w-8 h-8 text-[#6f42c1]" />
            <span className="text-2xl font-bold text-white">PromptCraft</span>
          </Link>
        </div>

        <Card className="bg-gray-900/50 border-gray-700 backdrop-blur-sm">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-white">欢迎回来</CardTitle>
            <CardDescription className="text-gray-400">登录到你的账户继续创作</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <form onSubmit={handleEmailLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-white">
                  邮箱
                </Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10 bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-[#6f42c1]"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-white">
                  密码
                </Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pl-10 bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-[#6f42c1]"
                    required
                  />
                </div>
              </div>

              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] hover:from-[#5a359a] hover:to-[#0099cc] text-white"
                disabled={loading || !supabase}
              >
                {loading ? "登录中..." : "登录"}
              </Button>
            </form>

            {showResendButton && (
              <div className="text-center">
                <Button
                  variant="outline"
                  onClick={handleResendVerification}
                  disabled={resendingEmail || !email}
                  className="border-[#6f42c1] text-[#6f42c1] hover:bg-[#6f42c1] hover:text-white"
                >
                  {resendingEmail ? "发送中..." : "重新发送验证邮件"}
                </Button>
              </div>
            )}

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full bg-gray-600" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-gray-900 px-2 text-gray-400">或者</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Button
                variant="outline"
                onClick={handleGoogleLogin}
                className="border-gray-600 text-white hover:bg-gray-800 bg-transparent"
                disabled={!supabase}
              >
                <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="currentColor"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                </svg>
                Google
              </Button>
              <Button
                variant="outline"
                onClick={handleGithubLogin}
                className="border-gray-600 text-white hover:bg-gray-800 bg-transparent"
                disabled={!supabase}
              >
                <Github className="w-4 h-4 mr-2" />
                GitHub
              </Button>
            </div>

            <div className="text-center text-sm text-gray-400">
              还没有账户？{" "}
              <Link href="/register" className="text-[#6f42c1] hover:text-[#8b5cf6] font-medium">
                立即注册
              </Link>
            </div>
          </CardContent>
        </Card>
        {supabase === null && (
          <p className="text-center text-xs text-red-400 mt-2">未检测到 Supabase 环境变量，登录功能已停用</p>
        )}
      </motion.div>
    </div>
  )
}
