"use client"

import { useUserStore } from '@/lib/user-store'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { getBrowserSupabase } from '@/lib/create-browser-supabase'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

export default function TestAuthPage() {
  const { user, isAuthenticated, isLoading, logout } = useUserStore()
  const router = useRouter()
  const supabase = getBrowserSupabase()

  const handleLogout = async () => {
    if (supabase) {
      await supabase.auth.signOut()
    }
    logout()
    router.push('/')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#0A0A1A] flex items-center justify-center">
        <div className="text-white">加载中...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#0A0A1A] p-6">
      <div className="max-w-2xl mx-auto">
        <Card className="bg-gray-900/50 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">用户状态测试页面</CardTitle>
            <CardDescription className="text-gray-400">
              测试 Zustand 用户状态管理功能
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-white">认证状态</h3>
              <p className="text-gray-300">
                已认证: {isAuthenticated ? '是' : '否'}
              </p>
              <p className="text-gray-300">
                加载中: {isLoading ? '是' : '否'}
              </p>
            </div>

            {user && (
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-white">用户信息</h3>
                <p className="text-gray-300">邮箱: {user.email}</p>
                <p className="text-gray-300">ID: {user.id}</p>
                <p className="text-gray-300">
                  创建时间: {new Date(user.created_at).toLocaleString()}
                </p>
                {user.user_metadata?.username && (
                  <p className="text-gray-300">
                    用户名: {user.user_metadata.username}
                  </p>
                )}
              </div>
            )}

            <div className="flex gap-4 pt-4">
              <Link href="/">
                <Button variant="outline">返回首页</Button>
              </Link>
              {isAuthenticated ? (
                <>
                  <Link href="/app">
                    <Button>进入应用</Button>
                  </Link>
                  <Button variant="destructive" onClick={handleLogout}>
                    退出登录
                  </Button>
                </>
              ) : (
                <>
                  <Link href="/login">
                    <Button>登录</Button>
                  </Link>
                  <Link href="/register">
                    <Button variant="outline">注册</Button>
                  </Link>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 