"use client"

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { Settings, Save, Upload, Download, Plus, Trash2 } from 'lucide-react'
import { useConfig, type ConfigData, type AIProvider } from '@/hooks/use-config'

const DEFAULT_CONFIG: ConfigData = {
  providers: [
    {
      provider: "openai",
      models: [
        {
          model: "anthropic/claude-3.7-sonnet",
          title: "Claude 3.7 Sonnet (OpenRouter)",
          baseURL: "https://openrouter.ai/api/v1",
          apiKey: "",
          features: ["vision"]
        },
        {
          model: "anthropic/claude-3.5-sonnet",
          title: "Claude 3.5 Sonnet (OpenRouter)",
          baseURL: "https://openrouter.ai/api/v1",
          apiKey: "",
          features: ["vision"]
        },
        {
          model: "openai/gpt-4o",
          title: "GPT-4o (OpenRouter)",
          baseURL: "https://openrouter.ai/api/v1",
          apiKey: "",
          features: ["vision"]
        },
        {
          model: "gpt-4o",
          title: "GPT-4o (302)",
          baseURL: "https://api.302.ai/v1",
          apiKey: "",
          features: ["vision"]
        }
      ]
    },
    {
      provider: "anthropic",
      models: [
        {
          model: "claude-3-7-sonnet-latest",
          title: "Claude 3.7 Sonnet (302)",
          baseURL: "https://api.302.ai/v1",
          apiKey: "",
          features: ["vision"]
        },
        {
          model: "claude-3-5-sonnet-latest",
          title: "Claude 3.5 Sonnet (302)",
          baseURL: "https://api.302.ai/v1",
          apiKey: "",
          features: ["vision"]
        }
      ]
    },
    {
      provider: "deepseek",
      models: [
        {
          model: "deepseek-chat",
          title: "DeepSeek-V3 Chat (DeepSeek)",
          baseURL: "https://api.deepseek.com/v1",
          apiKey: "",
          features: ["vision"]
        }
      ]
    },
    {
      provider: "google",
      models: [
        {
          model: "google/gemini-2.0-flash-001",
          title: "Gemini 2.0 Flash (Google)",
          baseURL: "https://openrouter.ai/api/v1",
          apiKey: "",
          features: ["vision"]
        }
      ]
    }
  ]
}

export default function ConfigPage() {
  const [importData, setImportData] = useState('')
  const { toast } = useToast()
  const { config, loading, saveConfig: saveConfigHook, setConfig } = useConfig()
  const [saving, setSaving] = useState(false)

  const currentConfig = config || DEFAULT_CONFIG

  const saveConfig = async () => {
    setSaving(true)
    try {
      await saveConfigHook(currentConfig)
      toast({
        title: "配置保存成功",
        description: "您的AI模型配置已保存",
      })
    } catch (error) {
      console.error('保存配置失败:', error)
      toast({
        title: "保存配置失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const initializeFromFile = async () => {
    setSaving(true)
    try {
      const response = await fetch('/api/config/init', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('初始化失败')
      }

      const result = await response.json()
      
      // 重新加载配置
      window.location.reload()
      
      toast({
        title: "初始化成功",
        description: "已从配置文件导入数据",
      })
    } catch (error) {
      console.error('初始化失败:', error)
      toast({
        title: "初始化失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const exportConfig = () => {
    const dataStr = JSON.stringify(currentConfig, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = 'ai-config.json'
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  const importConfig = () => {
    try {
      const parsedConfig = JSON.parse(importData) as ConfigData
      
      // 验证配置格式
      if (!parsedConfig || !parsedConfig.providers || !Array.isArray(parsedConfig.providers)) {
        throw new Error('配置格式不正确')
      }

      // 验证每个提供商的格式
      for (const provider of parsedConfig.providers) {
        if (!provider.provider || !provider.models || !Array.isArray(provider.models)) {
          throw new Error('提供商格式不正确')
        }
        
        for (const model of provider.models) {
          if (!model.model || !model.title || !model.baseURL) {
            throw new Error('模型格式不正确')
          }
        }
      }

      setConfig(parsedConfig)
      setImportData('')
      toast({
        title: "导入成功",
        description: "配置数据已导入，请保存以应用更改",
      })
    } catch (error) {
      console.error('导入配置失败:', error)
      toast({
        title: "导入失败",
        description: error instanceof Error ? error.message : "请检查JSON格式是否正确",
        variant: "destructive",
      })
    }
  }

  const updateModelField = (providerIndex: number, modelIndex: number, field: string, value: string) => {
    const newConfig = { ...currentConfig }
    if (newConfig.providers[providerIndex]?.models[modelIndex]) {
      newConfig.providers[providerIndex].models[modelIndex] = {
        ...newConfig.providers[providerIndex].models[modelIndex],
        [field]: value
      }
      setConfig(newConfig)
    }
  }

  const addModel = (providerIndex: number) => {
    const newConfig = { ...currentConfig }
    if (newConfig.providers[providerIndex]) {
      newConfig.providers[providerIndex].models.push({
        model: "",
        title: "",
        baseURL: "",
        apiKey: "",
        features: []
      })
      setConfig(newConfig)
    }
  }

  const removeModel = (providerIndex: number, modelIndex: number) => {
    const newConfig = { ...currentConfig }
    if (newConfig.providers[providerIndex]?.models) {
      newConfig.providers[providerIndex].models.splice(modelIndex, 1)
      setConfig(newConfig)
    }
  }

  const addProvider = () => {
    const newConfig = { ...currentConfig }
    newConfig.providers.push({
      provider: "",
      models: []
    })
    setConfig(newConfig)
  }

  const removeProvider = (providerIndex: number) => {
    const newConfig = { ...currentConfig }
    newConfig.providers.splice(providerIndex, 1)
    setConfig(newConfig)
  }

  const updateProviderName = (providerIndex: number, name: string) => {
    const newConfig = { ...currentConfig }
    if (newConfig.providers[providerIndex]) {
      newConfig.providers[providerIndex].provider = name
      setConfig(newConfig)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="w-16 h-16 rounded-full bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] flex items-center justify-center mx-auto mb-4 shadow-[0_0_20px_rgba(111,66,193,0.5)]">
            <Settings className="w-8 h-8 text-white animate-spin" />
          </div>
          <p className="text-gray-300 text-lg">加载配置中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)]"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(111,66,193,0.5)]">
              AI 模型配置
            </h1>
            <p className="text-gray-300 text-lg">管理您的AI模型提供商和API配置</p>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={initializeFromFile}
              disabled={saving}
              variant="outline"
              className="border-white/20 text-gray-300 hover:bg-white/10 bg-white/5 backdrop-blur-md hover:shadow-[0_0_20px_rgba(255,255,255,0.2)] transition-all duration-300"
            >
              <Upload className="w-4 h-4 mr-2" />
              从文件初始化
            </Button>
            <Button
              onClick={exportConfig}
              variant="outline"
              className="border-white/20 text-gray-300 hover:bg-white/10 bg-white/5 backdrop-blur-md hover:shadow-[0_0_20px_rgba(255,255,255,0.2)] transition-all duration-300"
            >
              <Download className="w-4 h-4 mr-2" />
              导出配置
            </Button>
            <Button
              onClick={saveConfig}
              disabled={saving}
              className="bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] hover:from-[#5a359a] hover:to-[#0099cc] shadow-[0_8px_32px_rgba(111,66,193,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.5)] hover:scale-105 transition-all duration-300"
            >
              <Save className="w-4 h-4 mr-2" />
              {saving ? '保存中...' : '保存配置'}
            </Button>
          </div>
        </div>
      </motion.div>

      {/* 导入配置 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(0,191,255,0.2)] transition-all duration-300">
          <div className="mb-4">
            <div className="flex items-center space-x-2 mb-2">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#00BFFF] to-[#0ea5e9] flex items-center justify-center shadow-[0_0_15px_rgba(0,191,255,0.5)]">
                <Upload className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white">导入配置</h3>
            </div>
            <p className="text-gray-400">
              粘贴JSON配置数据来导入设置。
              <a 
                href="/sample-config.json" 
                target="_blank" 
                className="text-[#00BFFF] hover:text-[#0ea5e9] underline ml-1 hover:drop-shadow-[0_0_10px_rgba(0,191,255,0.5)] transition-all duration-300"
              >
                查看示例配置
              </a>
            </p>
          </div>
          <div className="space-y-4">
            <Textarea
              placeholder="粘贴JSON配置数据..."
              value={importData}
              onChange={(e) => setImportData(e.target.value)}
              className="bg-white/5 border border-white/10 text-white placeholder-gray-400 min-h-32 focus:border-[#00BFFF] focus:shadow-[0_0_20px_rgba(0,191,255,0.3)] backdrop-blur-md transition-all duration-300"
            />
            <Button
              onClick={importConfig}
              disabled={!importData.trim()}
              variant="outline"
              className="border-white/20 text-gray-300 hover:bg-white/10 bg-white/5 backdrop-blur-md hover:shadow-[0_0_20px_rgba(255,255,255,0.2)] transition-all duration-300 disabled:opacity-50"
            >
              导入配置
            </Button>
          </div>
        </div>
      </motion.div>

      {/* 配置编辑器 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="space-y-6"
      >
        {currentConfig.providers.map((provider, providerIndex) => (
          <motion.div
            key={providerIndex}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.3 + providerIndex * 0.1 }}
            whileHover={{ scale: 1.02 }}
          >
            <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.2)] transition-all duration-300">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-4">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] flex items-center justify-center shadow-[0_0_15px_rgba(111,66,193,0.5)]">
                    <Settings className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white">提供商</h3>
                  <Input
                    value={provider.provider}
                    onChange={(e) => updateProviderName(providerIndex, e.target.value)}
                    className="bg-white/5 border border-white/10 text-white placeholder-gray-400 w-40 focus:border-[#6f42c1] focus:shadow-[0_0_20px_rgba(111,66,193,0.3)] backdrop-blur-md transition-all duration-300"
                    placeholder="提供商名称"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => addModel(providerIndex)}
                    size="sm"
                    variant="outline"
                    className="border-white/20 text-gray-300 hover:bg-white/10 bg-white/5 backdrop-blur-md hover:shadow-[0_0_20px_rgba(255,255,255,0.2)] transition-all duration-300"
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    添加模型
                  </Button>
                  <Button
                    onClick={() => removeProvider(providerIndex)}
                    size="sm"
                    variant="outline"
                    className="border-red-500/30 text-red-400 hover:bg-red-500/10 bg-red-500/5 backdrop-blur-md hover:shadow-[0_0_20px_rgba(239,68,68,0.3)] transition-all duration-300"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              
              <div className="space-y-4">
                {provider.models.map((model, modelIndex) => (
                  <div key={modelIndex} className="backdrop-blur-md bg-white/5 p-4 rounded-xl border border-white/10 hover:shadow-[0_8px_32px_rgba(111,66,193,0.1)] transition-all duration-300">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-semibold text-white flex items-center space-x-2">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-[#00BFFF] to-[#6f42c1] flex items-center justify-center text-xs text-white">
                          {modelIndex + 1}
                        </div>
                        <span>模型 {modelIndex + 1}</span>
                      </h4>
                      <Button
                        onClick={() => removeModel(providerIndex, modelIndex)}
                        size="sm"
                        variant="outline"
                        className="border-red-500/30 text-red-400 hover:bg-red-500/10 bg-red-500/5 backdrop-blur-md hover:shadow-[0_0_20px_rgba(239,68,68,0.3)] transition-all duration-300"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="text-gray-300">模型名称</Label>
                        <Input
                          value={model.model}
                          onChange={(e) => updateModelField(providerIndex, modelIndex, 'model', e.target.value)}
                          className="bg-white/5 border border-white/10 text-white placeholder-gray-400 focus:border-[#00BFFF] focus:shadow-[0_0_20px_rgba(0,191,255,0.3)] backdrop-blur-md transition-all duration-300"
                          placeholder="模型名称"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label className="text-gray-300">显示标题</Label>
                        <Input
                          value={model.title}
                          onChange={(e) => updateModelField(providerIndex, modelIndex, 'title', e.target.value)}
                          className="bg-white/5 border border-white/10 text-white placeholder-gray-400 focus:border-[#00BFFF] focus:shadow-[0_0_20px_rgba(0,191,255,0.3)] backdrop-blur-md transition-all duration-300"
                          placeholder="显示标题"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label className="text-gray-300">Base URL</Label>
                        <Input
                          value={model.baseURL}
                          onChange={(e) => updateModelField(providerIndex, modelIndex, 'baseURL', e.target.value)}
                          className="bg-white/5 border border-white/10 text-white placeholder-gray-400 focus:border-[#00BFFF] focus:shadow-[0_0_20px_rgba(0,191,255,0.3)] backdrop-blur-md transition-all duration-300"
                          placeholder="https://api.example.com/v1"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label className="text-gray-300">API Key</Label>
                        <Input
                          type="password"
                          value={model.apiKey}
                          onChange={(e) => updateModelField(providerIndex, modelIndex, 'apiKey', e.target.value)}
                          className="bg-white/5 border border-white/10 text-white placeholder-gray-400 focus:border-[#00BFFF] focus:shadow-[0_0_20px_rgba(0,191,255,0.3)] backdrop-blur-md transition-all duration-300"
                          placeholder="API密钥"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* 添加提供商 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="text-center"
      >
        <Button
          onClick={addProvider}
          variant="outline"
          className="border-white/20 text-gray-300 hover:bg-white/10 bg-white/5 backdrop-blur-md hover:shadow-[0_0_20px_rgba(255,255,255,0.2)] transition-all duration-300 px-8 py-3"
        >
          <Plus className="w-4 h-4 mr-2" />
          添加新提供商
        </Button>
      </motion.div>
    </div>
  )
} 