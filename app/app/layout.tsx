"use client"

import type React from "react"
import { AppSidebar } from "@/components/app-sidebar"
import { AppHeader } from "@/components/app-header"
import { RouteGuard } from "@/components/route-guard"
import { useUserStore } from "@/lib/user-store"

export default function AppLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user } = useUserStore()

  return (
    <RouteGuard requireAuth={true}>
      <div className="min-h-screen bg-[#0A0A1A] text-white">
        <div className="flex">
          <AppSidebar />
          <div className="flex-1 flex flex-col">
            <AppHeader user={user} />
            <main className="flex-1 p-6">{children}</main>
          </div>
        </div>
      </div>
    </RouteGuard>
  )
}
