"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Zap, Copy, RefreshCw, Sparkles, CheckCircle, Settings, Target, MessageSquare, Search, AlertTriangle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { useConfig } from "@/hooks/use-config"
import Link from "next/link"

interface EnhancedResult {
  original_input: string
  analysis: {
    specific_entity: string
    domain: string
    likely_scenario: string
    professional_level: string
    key_details: string
    confidence: number
  }
  enhancement: {
    enhanced_questions: {
      concise: string
      detailed: string[]
      analytical: string
    }
    disclaimer: string
  }
  provider: string
  model: string
}

export default function EnhancePage() {
  const [inputText, setInputText] = useState("")
  const [result, setResult] = useState<EnhancedResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [copiedText, setCopiedText] = useState<string | null>(null)
  const [selectedProvider, setSelectedProvider] = useState("")
  const [selectedModel, setSelectedModel] = useState("")
  const { toast } = useToast()
  const supabase = createClientComponentClient()
  const { config, loading: configLoading } = useConfig()

  const handleEnhance = async () => {
    if (!inputText.trim()) {
      toast({
        title: "请输入问题",
        description: "请先输入一个问题或描述",
        variant: "destructive",
      })
      return
    }

    // 检查是否有配置
    if (!config || !config.providers || config.providers.length === 0) {
      toast({
        title: "请先配置AI模型",
        description: "请前往配置页面设置AI模型",
        variant: "destructive",
      })
      return
    }

    // 检查是否选择了模型
    if (!selectedProvider || !selectedModel) {
      toast({
        title: "请选择AI模型",
        description: "请先选择提供商和模型",
        variant: "destructive",
      })
      return
    }

    setLoading(true)

    try {
      // 调用实际的API
      const response = await fetch('/api/ai/enhance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: inputText,
          provider: selectedProvider,
          model: selectedModel,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '增强失败')
      }

      const apiResult = await response.json()
      
      setResult(apiResult.data)

      // 保存到数据库
      const {
        data: { user },
      } = await supabase.auth.getUser()
      if (user) {
        await supabase.from("prompts").insert({
          user_id: user.id,
          source_text: inputText,
          result_text: JSON.stringify(apiResult.data),
          type: "enhanced",
        })
      }

      toast({
        title: "分析完成！",
        description: `成功识别领域：${apiResult.data.analysis.domain}`,
      })
    } catch (error) {
      console.error('增强失败:', error)
      toast({
        title: "增强失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedText(text)
      setTimeout(() => setCopiedText(null), 2000)
      toast({
        title: "已复制",
        description: "内容已复制到剪贴板",
      })
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      })
    }
  }

  const handleRegenerate = () => {
    if (inputText.trim()) {
      handleEnhance()
    }
  }

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center"
      >
        <div className="flex items-center justify-center space-x-2 mb-4">
          <div className="w-12 h-12 rounded-full bg-gradient-to-r from-[#6f42c1] to-[#8b5cf6] flex items-center justify-center shadow-[0_0_20px_rgba(111,66,193,0.5)]">
            <Zap className="w-6 h-6 text-white" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-[#6f42c1] to-[#8b5cf6] bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(111,66,193,0.5)]">
            智能增强
          </h1>
        </div>
        <p className="text-gray-300 text-lg">理解用户需求，生成专业问题表达</p>
      </motion.div>

      {/* Input Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.2)] transition-all duration-300">
          <div className="mb-4">
            <div className="flex items-center space-x-2 mb-2">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#6f42c1] to-[#8b5cf6] flex items-center justify-center shadow-[0_0_15px_rgba(111,66,193,0.5)]">
                <Sparkles className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white">输入你的问题或想法</h3>
            </div>
            <p className="text-gray-400">输入一个模糊的问题或想法，AI将识别领域并生成三种专业表达方式</p>
          </div>
          
          <div className="space-y-4">
            {/* 模型选择器 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">AI 提供商</label>
                <Select value={selectedProvider} onValueChange={(value) => {
                  setSelectedProvider(value)
                  setSelectedModel("") // 重置模型选择
                }}>
                  <SelectTrigger className="bg-white/5 border border-white/10 text-white backdrop-blur-md hover:bg-white/10 transition-all duration-300">
                    <SelectValue placeholder="选择提供商" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900/95 border border-white/10 backdrop-blur-xl">
                    {config?.providers?.map((provider) => (
                      <SelectItem key={provider.provider} value={provider.provider} className="text-white hover:bg-white/10 focus:bg-white/10">
                        {provider.provider}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">AI 模型</label>
                <Select value={selectedModel} onValueChange={setSelectedModel} disabled={!selectedProvider}>
                  <SelectTrigger className="bg-white/5 border border-white/10 text-white backdrop-blur-md hover:bg-white/10 transition-all duration-300 disabled:opacity-50">
                    <SelectValue placeholder="选择模型" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900/95 border border-white/10 backdrop-blur-xl">
                    {config?.providers
                      ?.find(p => p.provider === selectedProvider)
                      ?.models?.map((model) => (
                        <SelectItem key={model.model} value={model.model} className="text-white hover:bg-white/10 focus:bg-white/10">
                          {model.title}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 配置提示 */}
            {(!config || !config.providers || config.providers.length === 0) && (
              <div className="backdrop-blur-md bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/30 rounded-xl p-4">
                <div className="flex items-center space-x-2 text-yellow-400 mb-2">
                  <div className="w-6 h-6 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center">
                    <Settings className="w-3 h-3 text-white" />
                  </div>
                  <span className="font-medium">需要配置AI模型</span>
                </div>
                <p className="text-sm text-yellow-300 mb-3">
                  请先前往配置页面设置AI模型提供商和API密钥
                </p>
                <Link href="/app/config">
                  <Button className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white shadow-[0_8px_32px_rgba(245,158,11,0.3)] hover:shadow-[0_12px_48px_rgba(245,158,11,0.5)] hover:scale-105 transition-all duration-300">
                    前往配置
                  </Button>
                </Link>
              </div>
            )}
            
            <div className="relative">
              <Textarea
                placeholder="例如：前端页面报错、远达环保相关、如何学习Python等"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                className="min-h-[120px] bg-white/5 border border-white/10 text-white placeholder-gray-400 focus:border-[#6f42c1] focus:shadow-[0_0_20px_rgba(111,66,193,0.3)] resize-none backdrop-blur-md transition-all duration-300"
              />
              <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                {inputText.length} 字符
              </div>
            </div>
            
            <div className="flex justify-center">
              <Button
                onClick={handleEnhance}
                disabled={loading || !inputText.trim() || !selectedProvider || !selectedModel}
                className="bg-gradient-to-r from-[#6f42c1] to-[#8b5cf6] hover:from-[#5a359a] hover:to-[#7c3aed] px-8 py-3 text-lg font-semibold rounded-full shadow-[0_8px_32px_rgba(111,66,193,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.5)] hover:scale-105 transition-all duration-300"
              >
                {loading ? (
                  <>
                    <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
                    分析中...
                  </>
                ) : (
                  <>
                    <Zap className="w-5 h-5 mr-2" />
                    {selectedProvider && selectedModel ? 
                      `使用 ${config?.providers?.find(p => p.provider === selectedProvider)?.models?.find(m => m.model === selectedModel)?.title || selectedModel} 增强` 
                      : '智能增强 ✨'
                    }
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Results Section */}
      {result && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="space-y-6"
        >
          <div className="flex items-center justify-center space-x-4">
            <h2 className="text-2xl font-semibold bg-gradient-to-r from-[#6f42c1] to-[#8b5cf6] bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(111,66,193,0.5)]">
              分析结果
            </h2>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRegenerate}
              disabled={loading}
              className="border-white/20 text-gray-300 hover:bg-white/10 bg-white/5 backdrop-blur-md hover:shadow-[0_0_20px_rgba(255,255,255,0.2)] transition-all duration-300"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              重新生成
            </Button>
          </div>

          {/* 领域分析 */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.3)] transition-all duration-300"
          >
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-[#6f42c1] to-[#8b5cf6] flex items-center justify-center shadow-[0_0_15px_rgba(111,66,193,0.5)]">
                <Target className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white">领域与意图分析</h3>
            </div>
            
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div className="backdrop-blur-md bg-white/5 p-4 rounded-xl border-l-4 border-[#6f42c1]">
                <h4 className="text-sm font-semibold text-[#6f42c1] mb-2">识别实体</h4>
                <p className="text-white text-lg font-medium">{result.analysis.specific_entity}</p>
              </div>
              <div className="backdrop-blur-md bg-white/5 p-4 rounded-xl border-l-4 border-[#8b5cf6]">
                <h4 className="text-sm font-semibold text-[#8b5cf6] mb-2">专业领域</h4>
                <p className="text-white text-lg font-medium">{result.analysis.domain}</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div className="backdrop-blur-md bg-white/5 p-4 rounded-xl border-l-4 border-green-500">
                <h4 className="text-sm font-semibold text-green-500 mb-2">可能场景</h4>
                <p className="text-white text-lg font-medium">{result.analysis.likely_scenario}</p>
              </div>
              <div className="backdrop-blur-md bg-white/5 p-4 rounded-xl border-l-4 border-yellow-500">
                <h4 className="text-sm font-semibold text-yellow-500 mb-2">专业程度</h4>
                <p className="text-white text-lg font-medium">{result.analysis.professional_level}</p>
              </div>
            </div>
            
            <div className="backdrop-blur-md bg-white/5 p-4 rounded-xl border-l-4 border-[#00BFFF] mb-4">
              <h4 className="text-sm font-semibold text-[#00BFFF] mb-2">关键信息</h4>
              <p className="text-gray-300">{result.analysis.key_details}</p>
            </div>
            
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center space-x-2">
                <Badge className="bg-green-500/20 text-green-400 border border-green-500/30">
                  置信度: {Math.round(result.analysis.confidence * 100)}%
                </Badge>
                <Badge className="bg-blue-500/20 text-blue-400 border border-blue-500/30">
                  {result.provider} - {result.model}
                </Badge>
              </div>
            </div>
          </motion.div>

          {/* 问题增强结果 */}
          <div className="space-y-6">


                         {/* 启发性问题展示 */}
             <div className="space-y-4">
               <h3 className="text-xl font-semibold text-white text-center mb-6">
                 💡 为您生成的启发性问题
               </h3>
               
               <div className="grid gap-4">
                 {/* 简洁版 */}
                 <motion.div
                   initial={{ opacity: 0, x: -20 }}
                   animate={{ opacity: 1, x: 0 }}
                   transition={{ duration: 0.5, delay: 0.1 }}
                   className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.2)] transition-all duration-300 bg-green-500/10"
                 >
                   <div className="flex items-start justify-between mb-4">
                     <div className="flex items-center space-x-3">
                       <span className="text-2xl">🎯</span>
                       <div>
                         <h4 className="text-lg font-semibold text-green-400">
                           简洁版
                         </h4>
                         <Badge className="bg-green-500/10 text-green-400 border border-green-500/30 text-xs">
                           直击核心
                         </Badge>
                       </div>
                     </div>
                     <Button
                       variant="ghost"
                       size="sm"
                       onClick={() => handleCopy(result.enhancement.enhanced_questions.concise)}
                       className="text-gray-400 hover:text-white hover:bg-white/10 backdrop-blur-md transition-all duration-300"
                     >
                       {copiedText === result.enhancement.enhanced_questions.concise ? (
                         <CheckCircle className="w-4 h-4 text-green-400" />
                       ) : (
                         <Copy className="w-4 h-4" />
                       )}
                     </Button>
                   </div>
                   
                   <div className="backdrop-blur-md bg-white/5 p-4 rounded-xl border-l-4 border-green-500">
                     <p className="text-gray-300 leading-relaxed">
                       {result.enhancement.enhanced_questions.concise}
                     </p>
                   </div>
                 </motion.div>

                 {/* 详细版 */}
                 <motion.div
                   initial={{ opacity: 0, x: -20 }}
                   animate={{ opacity: 1, x: 0 }}
                   transition={{ duration: 0.5, delay: 0.2 }}
                   className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.2)] transition-all duration-300 bg-blue-500/10"
                 >
                   <div className="flex items-start justify-between mb-4">
                     <div className="flex items-center space-x-3">
                       <span className="text-2xl">🔍</span>
                       <div>
                         <h4 className="text-lg font-semibold text-blue-400">
                           详细版
                         </h4>
                         <Badge className="bg-blue-500/10 text-blue-400 border border-blue-500/30 text-xs">
                           {result.enhancement.enhanced_questions.detailed.length}个深度问题
                         </Badge>
                       </div>
                     </div>
                     <Button
                       variant="ghost"
                       size="sm"
                       onClick={() => handleCopy(result.enhancement.enhanced_questions.detailed.join('\n\n'))}
                       className="text-gray-400 hover:text-white hover:bg-white/10 backdrop-blur-md transition-all duration-300"
                     >
                       {copiedText === result.enhancement.enhanced_questions.detailed.join('\n\n') ? (
                         <CheckCircle className="w-4 h-4 text-green-400" />
                       ) : (
                         <Copy className="w-4 h-4" />
                       )}
                     </Button>
                   </div>
                   
                   <div className="space-y-3">
                     {result.enhancement.enhanced_questions.detailed.map((question, index) => (
                       <div key={index} className="backdrop-blur-md bg-white/5 p-4 rounded-xl border-l-4 border-blue-500 relative group">
                         <div className="flex items-start space-x-2">
                           <span className="text-blue-400 font-semibold text-sm mt-1">{index + 1}.</span>
                           <p className="text-gray-300 leading-relaxed text-sm flex-1">
                             {question}
                           </p>
                           <Button
                             variant="ghost"
                             size="sm"
                             onClick={() => handleCopy(question)}
                             className="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-white hover:bg-white/10 backdrop-blur-md transition-all duration-300 p-1 h-6 w-6"
                           >
                             {copiedText === question ? (
                               <CheckCircle className="w-3 h-3 text-green-400" />
                             ) : (
                               <Copy className="w-3 h-3" />
                             )}
                           </Button>
                         </div>
                       </div>
                     ))}
                   </div>
                 </motion.div>

                 {/* 分析版 */}
                 <motion.div
                   initial={{ opacity: 0, x: -20 }}
                   animate={{ opacity: 1, x: 0 }}
                   transition={{ duration: 0.5, delay: 0.3 }}
                   className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.2)] transition-all duration-300 bg-purple-500/10"
                 >
                   <div className="flex items-start justify-between mb-4">
                     <div className="flex items-center space-x-3">
                       <span className="text-2xl">⚡</span>
                       <div>
                         <h4 className="text-lg font-semibold text-purple-400">
                           分析版
                         </h4>
                         <Badge className="bg-purple-500/10 text-purple-400 border border-purple-500/30 text-xs">
                           结构化框架
                         </Badge>
                       </div>
                     </div>
                     <Button
                       variant="ghost"
                       size="sm"
                       onClick={() => handleCopy(result.enhancement.enhanced_questions.analytical)}
                       className="text-gray-400 hover:text-white hover:bg-white/10 backdrop-blur-md transition-all duration-300"
                     >
                       {copiedText === result.enhancement.enhanced_questions.analytical ? (
                         <CheckCircle className="w-4 h-4 text-green-400" />
                       ) : (
                         <Copy className="w-4 h-4" />
                       )}
                     </Button>
                   </div>
                   
                   <div className="backdrop-blur-md bg-white/5 p-4 rounded-xl border-l-4 border-purple-500">
                     <p className="text-gray-300 leading-relaxed">
                       {result.enhancement.enhanced_questions.analytical}
                     </p>
                   </div>
                 </motion.div>
               </div>
             </div>



            {/* 免责声明 */}
            {result.enhancement.disclaimer && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="backdrop-blur-xl bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/30 rounded-3xl p-6 shadow-[0_8px_32px_rgba(245,158,11,0.2)]"
              >
                <div className="flex items-center space-x-2 mb-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center shadow-[0_0_15px_rgba(245,158,11,0.5)]">
                    <AlertTriangle className="w-4 h-4 text-white" />
                  </div>
                  <h4 className="text-lg font-semibold text-yellow-400">重要提示</h4>
                </div>
                <p className="text-yellow-300 leading-relaxed">
                  {result.enhancement.disclaimer}
                </p>
              </motion.div>
            )}
          </div>
        </motion.div>
      )}

      {/* Tips Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <div className="backdrop-blur-xl bg-gradient-to-r from-[#6f42c1]/10 to-[#00BFFF]/10 border border-[#6f42c1]/30 rounded-3xl p-6 shadow-[0_8px_32px_rgba(111,66,193,0.2)]">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
              <span>💡</span>
              <span>使用说明</span>
            </h3>
          </div>
          <ul className="space-y-2 text-sm text-gray-300">
            <li>• <strong className="text-[#6f42c1]">深度分析</strong>：识别用户需求的领域、意图、背景、技术水平和紧急程度</li>
            <li>• <strong className="text-purple-400">专家角色</strong>：基于识别领域，化身为该领域的资深专家提供建议</li>
            <li>• <strong className="text-green-400">简洁版</strong>：专业化的精准问题表达，便于快速定位核心</li>
            <li>• <strong className="text-[#6f42c1]">详细版</strong>：包含技术背景、原因分析和排查思路的完整描述</li>
            <li>• <strong className="text-[#00BFFF]">探索版</strong>：基于专业经验的相关问题，覆盖实际工作场景</li>
            <li>• <strong className="text-yellow-400">专业建议</strong>：提供可操作的具体指导和最佳实践</li>
            <li>• <strong className="text-pink-400">智能识别</strong>：自动分析用户水平、问题背景和潜在需求</li>
            <li>• 建议输入简洁的问题描述，如"前端页面报错"、"远达环保相关"等</li>
          </ul>
        </div>
      </motion.div>
    </div>
  )
}
