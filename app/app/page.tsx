"use client";

import { motion } from "framer-motion";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Zap,
  Brain,
  Library,
  Settings,
  TrendingUp,
  Clock,
  Star,
  Database,
} from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { useToast } from "@/hooks/use-toast";

export default function AppDashboard() {
  const [stats, setStats] = useState({
    totalPrompts: 0,
    recentPrompts: 0,
    favoritePrompts: 0,
  });
  const [initializing, setInitializing] = useState(false);
  const supabase = createClientComponentClient();
  const { toast } = useToast();

  useEffect(() => {
    const fetchUserStats = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return;

      const { data: prompts } = await supabase
        .from("prompts")
        .select("*")
        .eq("user_id", user.id);

      if (prompts) {
        const now = new Date();
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        const recentPrompts = prompts.filter(
          (prompt) => new Date(prompt.created_at) > weekAgo
        );

        setStats({
          totalPrompts: prompts.length,
          recentPrompts: recentPrompts.length,
          favoritePrompts: 0, // 可以后续添加收藏功能
        });
      }
    };

    fetchUserStats();
  }, [supabase]);

  const initializeConfig = async () => {
    setInitializing(true);
    try {
      const response = await fetch("/api/config/init", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("初始化失败");
      }

      const result = await response.json();

      toast({
        title: "配置初始化成功",
        description: "已从config.json导入配置数据到数据库",
      });
    } catch (error) {
      console.error("初始化配置失败:", error);
      toast({
        title: "初始化失败",
        description: "请稍后重试或联系管理员",
        variant: "destructive",
      });
    } finally {
      setInitializing(false);
    }
  };

  const quickActions = [
    {
      title: "智能增强",
      description: "将简单想法转化为专业提示词",
      icon: <Zap className="w-6 h-6" />,
      href: "/app/enhance",
      color: "from-[#6f42c1] to-[#8b5cf6]",
      hoverColor: "hover:shadow-[0_12px_48px_rgba(111,66,193,0.3)]",
    },
    {
      title: "提示词生成",
      description: "基于意图理解生成完美提示词",
      icon: <Brain className="w-6 h-6" />,
      href: "/app/generate",
      color: "from-[#00BFFF] to-[#0ea5e9]",
      hoverColor: "hover:shadow-[0_12px_48px_rgba(0,191,255,0.3)]",
    },
    {
      title: "我的灵感库",
      description: "查看和管理你的所有创意成果",
      icon: <Library className="w-6 h-6" />,
      href: "/app/library",
      color: "from-[#ff007f] to-[#ec4899]",
      hoverColor: "hover:shadow-[0_12px_48px_rgba(255,0,127,0.3)]",
    },
    {
      title: "AI 配置管理",
      description: "管理你的AI模型提供商和配置",
      icon: <Settings className="w-6 h-6" />,
      href: "/app/config",
      color: "from-[#10b981] to-[#059669]",
      hoverColor: "hover:shadow-[0_12px_48px_rgba(16,185,129,0.3)]",
    },
  ];

  const statsCards = [
    {
      title: "总提示词",
      value: stats.totalPrompts,
      icon: <TrendingUp className="w-5 h-5" />,
      color: "text-[#6f42c1]",
      bgColor: "from-[#6f42c1] to-[#8b5cf6]",
    },
    {
      title: "本周新增",
      value: stats.recentPrompts,
      icon: <Clock className="w-5 h-5" />,
      color: "text-[#00BFFF]",
      bgColor: "from-[#00BFFF] to-[#0ea5e9]",
    },
    {
      title: "收藏数量",
      value: stats.favoritePrompts,
      icon: <Star className="w-5 h-5" />,
      color: "text-[#ff007f]",
      bgColor: "from-[#ff007f] to-[#ec4899]",
    },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 shadow-[0_8px_32px_rgba(0,0,0,0.3)]"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(111,66,193,0.5)]">
              欢迎回来！
            </h1>
            <p className="text-gray-300 text-lg">
              准备好创造一些令人惊叹的提示词了吗？
            </p>
          </div>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
      >
        {statsCards.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
            whileHover={{ scale: 1.05 }}
          >
            <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.2)] transition-all duration-300 group cursor-pointer">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400 mb-1">{stat.title}</p>
                  <p className="text-2xl font-bold text-white group-hover:text-[#00BFFF] transition-colors duration-300">
                    {stat.value}
                  </p>
                </div>
                <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${stat.bgColor} flex items-center justify-center shadow-[0_0_20px_rgba(111,66,193,0.5)] group-hover:shadow-[0_0_30px_rgba(111,66,193,0.8)] transition-all duration-300`}>
                  <div className="text-white">{stat.icon}</div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <h2 className="text-2xl font-semibold mb-6 bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(111,66,193,0.5)]">
          快速操作
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickActions.map((action, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              whileHover={{ 
                rotateY: 5, 
                rotateX: 5,
                scale: 1.05,
                transition: { duration: 0.3 }
              }}
              style={{ perspective: "1000px" }}
            >
              <Link href={action.href}>
                <div className={`backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] ${action.hoverColor} transition-all duration-300 group cursor-pointer h-full`}>
                  <div className="mb-4">
                    <div
                      className={`w-12 h-12 rounded-full bg-gradient-to-r ${action.color} flex items-center justify-center shadow-[0_0_20px_rgba(111,66,193,0.5)] group-hover:scale-110 group-hover:shadow-[0_0_30px_rgba(111,66,193,0.8)] transition-all duration-300`}
                    >
                      <div className="text-white">{action.icon}</div>
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white group-hover:text-[#00BFFF] transition-colors duration-300">
                    {action.title}
                  </h3>
                  <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                    {action.description}
                  </p>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-semibold bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(111,66,193,0.5)]">
            最近活动
          </h2>
          <Link href="/app/library">
            <Button
              variant="outline"
              className="border-white/20 text-gray-300 hover:bg-white/10 bg-white/5 backdrop-blur-md hover:shadow-[0_0_20px_rgba(255,255,255,0.2)] transition-all duration-300"
            >
              查看全部
            </Button>
          </Link>
        </div>

        <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.2)] transition-all duration-300">
          {stats.totalPrompts > 0 ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] flex items-center justify-center mx-auto mb-4 shadow-[0_0_20px_rgba(111,66,193,0.5)]">
                <Library className="w-8 h-8 text-white" />
              </div>
              <p className="text-gray-300 mb-4 text-lg">
                你已经创建了 {stats.totalPrompts} 个提示词
              </p>
              <Link href="/app/library">
                <Button className="bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] hover:from-[#5a359a] hover:to-[#0099cc] shadow-[0_8px_32px_rgba(111,66,193,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.5)] hover:scale-105 transition-all duration-300">
                  查看我的灵感库
                </Button>
              </Link>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-[#00BFFF] to-[#6f42c1] flex items-center justify-center mx-auto mb-4 shadow-[0_0_20px_rgba(0,191,255,0.5)]">
                <Brain className="w-8 h-8 text-white" />
              </div>
              <p className="text-gray-300 mb-4 text-lg">还没有创建任何提示词</p>
              <p className="text-sm text-gray-400 mb-6">
                开始你的第一次创作吧！
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link href="/app/enhance">
                  <Button className="bg-gradient-to-r from-[#6f42c1] to-[#8b5cf6] hover:from-[#5a359a] hover:to-[#7c3aed] shadow-[0_8px_32px_rgba(111,66,193,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.5)] hover:scale-105 transition-all duration-300">
                    增强提示词
                  </Button>
                </Link>
                <Link href="/app/generate">
                  <Button className="bg-gradient-to-r from-[#00BFFF] to-[#0ea5e9] hover:from-[#0099cc] hover:to-[#0284c7] shadow-[0_8px_32px_rgba(0,191,255,0.3)] hover:shadow-[0_12px_48px_rgba(0,191,255,0.5)] hover:scale-105 transition-all duration-300">
                    生成提示词
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
}
