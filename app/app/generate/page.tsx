"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Brain, Copy, RefreshCw, Lightbulb, CheckCircle, Wand2, Settings } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { useConfig } from "@/hooks/use-config"
import Link from "next/link"

export default function GeneratePage() {
  const [inputIdea, setInputIdea] = useState("")
  const [generatedPrompt, setGeneratedPrompt] = useState("")
  const [intentAnalysis, setIntentAnalysis] = useState("")
  const [loading, setLoading] = useState(false)
  const [copied, setCopied] = useState(false)
  const [selectedProvider, setSelectedProvider] = useState("")
  const [selectedModel, setSelectedModel] = useState("")
  const { toast } = useToast()
  const supabase = createClientComponentClient()
  const { config, loading: configLoading } = useConfig()

  const handleGenerate = async () => {
    if (!inputIdea.trim()) {
      toast({
        title: "请输入创意想法",
        description: "请先输入一个简单的创意想法",
        variant: "destructive",
      })
      return
    }

    // 检查是否有配置
    if (!config || !config.providers || config.providers.length === 0) {
      toast({
        title: "请先配置AI模型",
        description: "请前往配置页面设置AI模型",
        variant: "destructive",
      })
      return
    }

    // 检查是否选择了模型
    if (!selectedProvider || !selectedModel) {
      toast({
        title: "请选择AI模型",
        description: "请先选择提供商和模型",
        variant: "destructive",
      })
      return
    }

    setLoading(true)

    try {
      // 调用实际的API
      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: inputIdea,
          provider: selectedProvider,
          model: selectedModel,
        }),
      })

      if (!response.ok) {
        throw new Error('生成失败')
      }

      const result = await response.json()

      // 修复：使用正确的数据结构
      const intentAnalysis = result.data?.intentAnalysis || result.intentAnalysis
      const generatedPrompt = result.data?.generatedPrompt || result.generatedPrompt

      setIntentAnalysis(intentAnalysis)
      setGeneratedPrompt(generatedPrompt)

      // 保存到数据库
      const {
        data: { user },
      } = await supabase.auth.getUser()
      if (user && generatedPrompt) {
        await supabase.from("prompts").insert({
          user_id: user.id,
          source_text: inputIdea,
          result_text: generatedPrompt,
          type: "generated",
        })
      }

      toast({
        title: "生成完成！",
        description: "已成功生成专业提示词",
      })
    } catch (error) {
      console.error('生成失败:', error)
      toast({
        title: "生成失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(generatedPrompt)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      toast({
        title: "已复制",
        description: "提示词已复制到剪贴板",
      })
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      })
    }
  }

  const handleRegenerate = () => {
    if (inputIdea.trim()) {
      handleGenerate()
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center"
      >
        <div className="flex items-center justify-center space-x-2 mb-4">
          <div className="w-12 h-12 rounded-full bg-gradient-to-r from-[#00BFFF] to-[#0ea5e9] flex items-center justify-center shadow-[0_0_20px_rgba(0,191,255,0.5)]">
            <Brain className="w-6 h-6 text-white" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-[#00BFFF] to-[#0ea5e9] bg-clip-text text-transparent drop-shadow-[0_0_20px_rgba(0,191,255,0.5)]">
            提示词生成
          </h1>
        </div>
        <p className="text-gray-300 text-lg">两阶段智能生成：理解意图 → 完善提示词</p>
      </motion.div>

      {/* Input Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(0,191,255,0.2)] transition-all duration-300">
          <div className="mb-4">
            <div className="flex items-center space-x-2 mb-2">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#00BFFF] to-[#0ea5e9] flex items-center justify-center shadow-[0_0_15px_rgba(0,191,255,0.5)]">
                <Lightbulb className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white">输入你的想法</h3>
            </div>
            <p className="text-gray-400">输入您的需求，AI将分两个阶段处理：先理解您的意图，再生成结构化的专业提示词</p>
          </div>
          
          <div className="space-y-4">
            {/* 模型选择器 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">AI 提供商</label>
                <Select value={selectedProvider} onValueChange={(value) => {
                  setSelectedProvider(value)
                  setSelectedModel("") // 重置模型选择
                }}>
                  <SelectTrigger className="bg-white/5 border border-white/10 text-white backdrop-blur-md hover:bg-white/10 transition-all duration-300">
                    <SelectValue placeholder="选择提供商" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900/95 border border-white/10 backdrop-blur-xl">
                    {config?.providers?.map((provider) => (
                      <SelectItem key={provider.provider} value={provider.provider} className="text-white hover:bg-white/10 focus:bg-white/10">
                        {provider.provider}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">AI 模型</label>
                <Select value={selectedModel} onValueChange={setSelectedModel} disabled={!selectedProvider}>
                  <SelectTrigger className="bg-white/5 border border-white/10 text-white backdrop-blur-md hover:bg-white/10 transition-all duration-300 disabled:opacity-50">
                    <SelectValue placeholder="选择模型" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900/95 border border-white/10 backdrop-blur-xl">
                    {config?.providers
                      ?.find(p => p.provider === selectedProvider)
                      ?.models?.map((model) => (
                        <SelectItem key={model.model} value={model.model} className="text-white hover:bg-white/10 focus:bg-white/10">
                          {model.title}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 配置提示 */}
            {(!config || !config.providers || config.providers.length === 0) && (
              <div className="backdrop-blur-md bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/30 rounded-xl p-4">
                <div className="flex items-center space-x-2 text-yellow-400 mb-2">
                  <div className="w-6 h-6 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center">
                    <Settings className="w-3 h-3 text-white" />
                  </div>
                  <span className="font-medium">需要配置AI模型</span>
                </div>
                <p className="text-sm text-yellow-300 mb-3">
                  请先前往配置页面设置AI模型提供商和API密钥
                </p>
                <Link href="/app/config">
                  <Button className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white shadow-[0_8px_32px_rgba(245,158,11,0.3)] hover:shadow-[0_12px_48px_rgba(245,158,11,0.5)] hover:scale-105 transition-all duration-300">
                    前往配置
                  </Button>
                </Link>
              </div>
            )}
            
            <div className="relative">
              <textarea
                placeholder="例如：渤海汽车年报、如何学习AI相关知识、制作产品使用说明书、分析市场竞争对手"
                value={inputIdea}
                onChange={(e) => setInputIdea(e.target.value)}
                className="w-full min-h-[120px] bg-white/5 border border-white/10 text-white placeholder-gray-400 focus:border-[#00BFFF] focus:shadow-[0_0_20px_rgba(0,191,255,0.3)] text-lg p-4 rounded-xl resize-none focus:outline-none backdrop-blur-md transition-all duration-300"
                onKeyPress={(e) => e.key === "Enter" && e.ctrlKey && !loading && handleGenerate()}
              />
              <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                {inputIdea.length} 字符
              </div>
            </div>
            
            <div className="flex justify-center">
              <Button
                onClick={handleGenerate}
                disabled={loading || !inputIdea.trim() || !selectedProvider || !selectedModel}
                className="bg-gradient-to-r from-[#00BFFF] to-[#0ea5e9] hover:from-[#0099cc] hover:to-[#0284c7] px-8 py-3 text-lg font-semibold rounded-full shadow-[0_8px_32px_rgba(0,191,255,0.3)] hover:shadow-[0_12px_48px_rgba(0,191,255,0.5)] hover:scale-105 transition-all duration-300"
              >
                {loading ? (
                  <>
                    <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Brain className="w-5 h-5 mr-2" />
                    {selectedProvider && selectedModel ? 
                      `使用 ${config?.providers?.find(p => p.provider === selectedProvider)?.models?.find(m => m.model === selectedModel)?.title || selectedModel} 生成` 
                      : '生成 💡'
                    }
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Intent Analysis */}
      {intentAnalysis && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="backdrop-blur-xl bg-gradient-to-r from-[#00BFFF]/10 to-[#6f42c1]/10 border border-[#00BFFF]/30 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,191,255,0.2)]">
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#00BFFF] to-[#6f42c1] flex items-center justify-center shadow-[0_0_20px_rgba(0,191,255,0.5)]">
                  <Wand2 className="w-4 h-4 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-[#00BFFF] drop-shadow-[0_0_10px_rgba(0,191,255,0.5)]">
                  第一阶段：意图理解
                </h3>
              </div>
              <p className="text-gray-400">
                AI 分析您的输入，理解您的真实需求和意图
              </p>
            </div>
            <div className="backdrop-blur-md bg-white/5 p-4 rounded-xl border-l-4 border-[#00BFFF]">
              <div className="text-sm text-gray-300 whitespace-pre-wrap font-sans leading-relaxed">
                {intentAnalysis}
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Generated Result */}
      {generatedPrompt && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(111,66,193,0.2)] transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#6f42c1] to-[#00BFFF] flex items-center justify-center shadow-[0_0_20px_rgba(111,66,193,0.5)]">
                    <Brain className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-[#6f42c1] drop-shadow-[0_0_10px_rgba(111,66,193,0.5)]">
                    第二阶段：完善的提示词
                  </h3>
                </div>
                <p className="text-gray-400">
                  基于意图分析，生成结构化、专业的完整提示词
                </p>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRegenerate}
                  disabled={loading}
                  className="border-white/20 text-gray-300 hover:bg-white/10 bg-white/5 backdrop-blur-md hover:shadow-[0_0_20px_rgba(255,255,255,0.2)] transition-all duration-300"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  重新生成
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopy}
                  className="border-white/20 text-gray-300 hover:bg-white/10 bg-white/5 backdrop-blur-md hover:shadow-[0_0_20px_rgba(255,255,255,0.2)] transition-all duration-300"
                >
                  {copied ? (
                    <CheckCircle className="w-4 h-4 mr-2 text-green-400" />
                  ) : (
                    <Copy className="w-4 h-4 mr-2" />
                  )}
                  {copied ? "已复制" : "复制"}
                </Button>
              </div>
            </div>
            
            <div className="backdrop-blur-md bg-white/5 p-6 rounded-xl border-l-4 border-[#6f42c1] mb-4">
              <div className="text-gray-300 leading-relaxed whitespace-pre-wrap font-sans">
                {generatedPrompt}
              </div>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <Badge className="bg-[#00BFFF]/20 text-[#00BFFF] border border-[#00BFFF]/30 backdrop-blur-md shadow-[0_0_10px_rgba(0,191,255,0.3)]">
                两阶段生成
              </Badge>
              <Badge className="bg-[#6f42c1]/20 text-[#6f42c1] border border-[#6f42c1]/30 backdrop-blur-md shadow-[0_0_10px_rgba(111,66,193,0.3)]">
                意图理解
              </Badge>
              <Badge className="bg-[#ff007f]/20 text-[#ff007f] border border-[#ff007f]/30 backdrop-blur-md shadow-[0_0_10px_rgba(255,0,127,0.3)]">
                结构化输出
              </Badge>
              {selectedProvider && selectedModel && (
                <Badge className="bg-green-500/20 text-green-400 border border-green-500/30 backdrop-blur-md shadow-[0_0_10px_rgba(34,197,94,0.3)]">
                  {config?.providers?.find(p => p.provider === selectedProvider)?.models?.find(m => m.model === selectedModel)?.title || selectedModel}
                </Badge>
              )}
            </div>
          </div>
        </motion.div>
      )}

      {/* Tips Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <div className="backdrop-blur-xl bg-gradient-to-r from-[#00BFFF]/10 to-[#6f42c1]/10 border border-[#00BFFF]/30 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,191,255,0.2)]">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
              <span>💡</span>
              <span>使用技巧</span>
            </h3>
          </div>
          <ul className="space-y-2 text-sm text-gray-300">
            <li>• <strong className="text-[#00BFFF]">第一步</strong>：选择AI提供商和模型（需要先在配置页面设置API密钥）</li>
            <li>• <strong className="text-[#00BFFF]">第二步</strong>：输入您的需求，如"渤海汽车年报"、"如何学习AI相关知识"</li>
            <li>• <strong className="text-[#6f42c1]">两阶段处理</strong>：AI首先理解您的意图，然后生成结构化的完整提示词</li>
            <li>• <strong className="text-[#6f42c1]">智能转换</strong>：简单输入 → 意图分析 → 专业提示词</li>
            <li>• <strong className="text-[#ff007f]">结构化输出</strong>：生成的提示词包含角色定位、任务描述、格式要求等</li>
            <li>• <strong className="text-[#ff007f]">实用性强</strong>：可直接复制使用，适用于各种AI应用场景</li>
            <li>• 使用 <kbd className="px-2 py-1 bg-white/10 rounded text-xs">Ctrl+Enter</kbd> 快速生成提示词</li>
          </ul>
        </div>
      </motion.div>
    </div>
  )
}
