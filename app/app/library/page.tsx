"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Library,
  Search,
  Filter,
  Trash2,
  Copy,
  Eye,
  Calendar,
  Zap,
  Brain,
  ChevronLeft,
  ChevronRight,
  FileText,
  Sparkles,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";

// 定义类型
interface Prompt {
  id: string;
  user_id: string;
  source_text: string | null;
  result_text: string;
  type: "enhanced" | "generated";
  created_at: string;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export default function LibraryPage() {
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });
  
  // 筛选和搜索状态
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [currentSearchTerm, setCurrentSearchTerm] = useState(""); // 实际用于请求的搜索词
  
  // 对话框状态
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const { toast } = useToast();
  const supabase = createClientComponentClient();

  // 获取灵感库数据
  const fetchPrompts = async (page: number = 1, search?: string, type?: string) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
      });

      if (search) {
        params.append("search", search);
      }

      if (type && type !== "all") {
        params.append("type", type);
      }

      const response = await fetch(`/api/library?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "获取数据失败");
      }

      setPrompts(data.prompts);
      setPagination(data.pagination);
    } catch (error) {
      console.error("获取灵感库数据失败:", error);
      toast({
        title: "获取数据失败",
        description: "请稍后重试",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 删除提示词
  const deletePrompt = async (id: string) => {
    setDeleteLoading(true);
    try {
      const response = await fetch(`/api/library/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "删除失败");
      }

      toast({
        title: "删除成功",
        description: "记录已从灵感库中删除",
      });

      // 重新获取数据
      fetchPrompts(pagination.page, currentSearchTerm, typeFilter);
      setDeleteDialogOpen(false);
      setSelectedPrompt(null);
    } catch (error) {
      console.error("删除失败:", error);
      toast({
        title: "删除失败",
        description: "请稍后重试",
        variant: "destructive",
      });
    } finally {
      setDeleteLoading(false);
    }
  };

  // 复制文本
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "复制成功",
        description: "内容已复制到剪贴板",
      });
    } catch (error) {
      toast({
        title: "复制失败",
        description: "请手动选择文本复制",
        variant: "destructive",
      });
    }
  };

  // 处理搜索
  const handleSearch = () => {
    setCurrentSearchTerm(searchTerm);
    fetchPrompts(1, searchTerm, typeFilter);
  };

  // 处理筛选
  const handleTypeFilter = (type: string) => {
    setTypeFilter(type);
    fetchPrompts(1, currentSearchTerm, type);
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // 获取类型图标和颜色
  const getTypeInfo = (type: "enhanced" | "generated") => {
    if (type === "enhanced") {
      return {
        icon: <Zap className="w-4 h-4" />,
        label: "智能增强",
        color: "from-[#6f42c1] to-[#8b5cf6]",
        bgColor: "bg-[#6f42c1]/10",
        borderColor: "border-[#6f42c1]/30",
      };
    } else {
      return {
        icon: <Brain className="w-4 h-4" />,
        label: "提示词生成",
        color: "from-[#00BFFF] to-[#0ea5e9]",
        bgColor: "bg-[#00BFFF]/10",
        borderColor: "border-[#00BFFF]/30",
      };
    }
  };

  // 初始加载
  useEffect(() => {
    fetchPrompts();
  }, []);

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 shadow-[0_8px_32px_rgba(0,0,0,0.3)]"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 rounded-2xl bg-gradient-to-r from-[#ff007f] to-[#ec4899] shadow-[0_8px_32px_rgba(255,0,127,0.3)]">
              <Library className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-[#ff007f] to-[#ec4899] bg-clip-text text-transparent">
                我的灵感库
              </h1>
              <p className="text-gray-300 text-lg">
                管理和浏览你所有的创意提示词记录
              </p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-white">{pagination.total}</p>
            <p className="text-sm text-gray-400">总记录数</p>
          </div>
        </div>
      </motion.div>

      {/* 搜索和筛选 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
        className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)]"
      >
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              placeholder="搜索你的提示词..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleSearch()}
              className="pl-10 bg-white/5 border-white/10 text-white placeholder-gray-400"
            />
          </div>
          <Select value={typeFilter} onValueChange={handleTypeFilter}>
            <SelectTrigger className="w-full md:w-48 bg-white/5 border-white/10 text-white">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue placeholder="选择类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="enhanced">智能增强</SelectItem>
              <SelectItem value="generated">提示词生成</SelectItem>
            </SelectContent>
          </Select>
          <Button
            onClick={handleSearch}
            className="bg-gradient-to-r from-[#ff007f] to-[#ec4899] hover:from-[#ff007f]/80 hover:to-[#ec4899]/80 text-white"
          >
            搜索
          </Button>
        </div>
      </motion.div>

      {/* 记录列表 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="space-y-4"
      >
        {loading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div
                key={index}
                className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 animate-pulse"
              >
                <div className="h-4 bg-white/10 rounded mb-4"></div>
                <div className="h-20 bg-white/10 rounded mb-4"></div>
                <div className="flex justify-between">
                  <div className="h-4 bg-white/10 rounded w-24"></div>
                  <div className="h-4 bg-white/10 rounded w-32"></div>
                </div>
              </div>
            ))}
          </div>
        ) : prompts.length === 0 ? (
          <div className="text-center py-16 backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl">
            <Sparkles className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-300 mb-2">
              {currentSearchTerm || typeFilter !== "all" ? "未找到匹配的记录" : "还没有任何记录"}
            </h3>
            <p className="text-gray-400 mb-6">
              {currentSearchTerm || typeFilter !== "all" ? 
                "尝试调整搜索条件或筛选器" : 
                "开始创建你的第一个提示词吧！"
              }
            </p>
            {(!currentSearchTerm && typeFilter === "all") && (
              <div className="flex justify-center space-x-4">
                <Button asChild className="bg-gradient-to-r from-[#6f42c1] to-[#8b5cf6]">
                  <a href="/app/enhance">智能增强</a>
                </Button>
                <Button asChild className="bg-gradient-to-r from-[#00BFFF] to-[#0ea5e9]">
                  <a href="/app/generate">提示词生成</a>
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            <AnimatePresence>
              {prompts.map((prompt, index) => {
                const typeInfo = getTypeInfo(prompt.type);
                return (
                  <motion.div
                    key={prompt.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_48px_rgba(255,0,127,0.2)] transition-all duration-300 group"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <Badge
                          className={`${typeInfo.bgColor} ${typeInfo.borderColor} text-white border flex items-center space-x-2`}
                        >
                          {typeInfo.icon}
                          <span>{typeInfo.label}</span>
                        </Badge>
                        <div className="flex items-center text-sm text-gray-400">
                          <Calendar className="w-4 h-4 mr-1" />
                          {formatDate(prompt.created_at)}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedPrompt(prompt);
                            setViewDialogOpen(true);
                          }}
                          className="opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-white"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(prompt.result_text)}
                          className="opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-white"
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedPrompt(prompt);
                            setDeleteDialogOpen(true);
                          }}
                          className="opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-red-400"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {prompt.source_text && (
                      <div className="mb-4 p-4 bg-white/5 rounded-2xl border border-white/10">
                        <p className="text-sm text-gray-400 mb-2 flex items-center">
                          <FileText className="w-4 h-4 mr-2" />
                          原始输入
                        </p>
                        <p className="text-gray-300 text-sm leading-relaxed">
                          {prompt.source_text}
                        </p>
                      </div>
                    )}

                    <div className="p-4 bg-gradient-to-r from-white/5 to-white/10 rounded-2xl border border-white/10">
                      <p className="text-sm text-gray-400 mb-2 flex items-center">
                        <Sparkles className="w-4 h-4 mr-2" />
                        生成结果
                      </p>
                      <p className="text-white leading-relaxed">
                        {prompt.result_text.length > 200
                          ? `${prompt.result_text.substring(0, 200)}...`
                          : prompt.result_text
                        }
                      </p>
                    </div>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>
        )}
      </motion.div>

      {/* 分页 */}
      {pagination.totalPages > 1 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="flex justify-center items-center space-x-4 backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-4"
        >
          <Button
            variant="ghost"
            size="sm"
            disabled={!pagination.hasPrev}
            onClick={() => fetchPrompts(pagination.page - 1, currentSearchTerm, typeFilter)}
            className="text-gray-400 hover:text-white disabled:opacity-50"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            上一页
          </Button>
          
          <span className="text-gray-300">
            第 {pagination.page} 页，共 {pagination.totalPages} 页
          </span>

          <Button
            variant="ghost"
            size="sm"
            disabled={!pagination.hasNext}
            onClick={() => fetchPrompts(pagination.page + 1, currentSearchTerm, typeFilter)}
            className="text-gray-400 hover:text-white disabled:opacity-50"
          >
            下一页
            <ChevronRight className="w-4 h-4 ml-1" />
          </Button>
        </motion.div>
      )}

      {/* 查看详情对话框 */}
      <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto bg-gray-900 border-gray-700 text-white">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              {selectedPrompt && (
                <>
                  {getTypeInfo(selectedPrompt.type).icon}
                  <span>{getTypeInfo(selectedPrompt.type).label}</span>
                </>
              )}
            </DialogTitle>
            <DialogDescription className="text-gray-400">
              创建时间：{selectedPrompt && formatDate(selectedPrompt.created_at)}
            </DialogDescription>
          </DialogHeader>
          
          {selectedPrompt && (
            <div className="space-y-6">
              {selectedPrompt.source_text && (
                <div>
                  <h4 className="text-lg font-semibold mb-3 flex items-center">
                    <FileText className="w-5 h-5 mr-2" />
                    原始输入
                  </h4>
                  <div className="p-4 bg-white/5 rounded-lg border border-white/10">
                    <p className="text-gray-300 leading-relaxed whitespace-pre-wrap">
                      {selectedPrompt.source_text}
                    </p>
                  </div>
                </div>
              )}
              
              <div>
                <h4 className="text-lg font-semibold mb-3 flex items-center">
                  <Sparkles className="w-5 h-5 mr-2" />
                  生成结果
                </h4>
                <div className="p-4 bg-gradient-to-r from-white/5 to-white/10 rounded-lg border border-white/10">
                  <p className="text-white leading-relaxed whitespace-pre-wrap">
                    {selectedPrompt.result_text}
                  </p>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button
              variant="ghost"
              onClick={() => setViewDialogOpen(false)}
              className="text-gray-400 hover:text-white"
            >
              关闭
            </Button>
            <Button
              onClick={() => selectedPrompt && copyToClipboard(selectedPrompt.result_text)}
              className="bg-gradient-to-r from-[#ff007f] to-[#ec4899] hover:from-[#ff007f]/80 hover:to-[#ec4899]/80"
            >
              <Copy className="w-4 h-4 mr-2" />
              复制结果
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="bg-gray-900 border-gray-700 text-white">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2 text-red-400">
              <Trash2 className="w-5 h-5" />
              <span>确认删除</span>
            </DialogTitle>
            <DialogDescription className="text-gray-400">
              确定要删除这条记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter>
            <Button
              variant="ghost"
              onClick={() => setDeleteDialogOpen(false)}
              className="text-gray-400 hover:text-white"
              disabled={deleteLoading}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={() => selectedPrompt && deletePrompt(selectedPrompt.id)}
              disabled={deleteLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteLoading ? "删除中..." : "确认删除"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 