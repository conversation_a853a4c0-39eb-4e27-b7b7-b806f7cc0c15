"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  RefreshCw, 
  Database, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  XCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface StatsData {
  total_prompts_generated: number;
  last_updated: string;
  real_time_count: number;
  is_data_consistent: boolean;
  data_source: string;
}

export default function StatsAdminPage() {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const { toast } = useToast();

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/stats/global");
      const data = await response.json();
      
      if (response.ok) {
        setStats(data);
      } else {
        throw new Error(data.error || "获取统计数据失败");
      }
    } catch (error) {
      console.error("获取统计数据失败:", error);
      toast({
        title: "获取失败",
        description: error instanceof Error ? error.message : "无法获取统计数据",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateStats = async () => {
    try {
      setUpdating(true);
      const response = await fetch("/api/stats/global", {
        method: "POST",
      });
      const data = await response.json();
      
      if (response.ok) {
        toast({
          title: "更新成功",
          description: "统计数据已手动更新",
        });
        await fetchStats(); // 重新获取数据
      } else {
        throw new Error(data.error || "更新统计数据失败");
      }
    } catch (error) {
      console.error("更新统计数据失败:", error);
      toast({
        title: "更新失败",
        description: error instanceof Error ? error.message : "无法更新统计数据",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">统计数据管理</h1>
        <div className="flex space-x-2">
          <Button 
            onClick={fetchStats} 
            variant="outline"
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button 
            onClick={updateStats}
            disabled={updating}
          >
            <Database className={`w-4 h-4 mr-2 ${updating ? 'animate-pulse' : ''}`} />
            {updating ? '更新中...' : '手动更新'}
          </Button>
        </div>
      </div>

      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 总数统计 */}
          <Card className="p-6">
            <div className="flex items-center space-x-2 mb-4">
              <TrendingUp className="w-5 h-5 text-blue-500" />
              <h3 className="text-lg font-semibold">总提示词数量</h3>
            </div>
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {stats.total_prompts_generated.toLocaleString()}
            </div>
            <p className="text-sm text-gray-600">
              存储在统计表中的数量
            </p>
          </Card>

          {/* 实时数量 */}
          <Card className="p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Database className="w-5 h-5 text-green-500" />
              <h3 className="text-lg font-semibold">实时数量</h3>
            </div>
            <div className="text-3xl font-bold text-green-600 mb-2">
              {stats.real_time_count.toLocaleString()}
            </div>
            <p className="text-sm text-gray-600">
              从 prompts 表直接统计
            </p>
          </Card>

          {/* 数据一致性 */}
          <Card className="p-6">
            <div className="flex items-center space-x-2 mb-4">
              {stats.is_data_consistent ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <AlertTriangle className="w-5 h-5 text-yellow-500" />
              )}
              <h3 className="text-lg font-semibold">数据一致性</h3>
            </div>
            <div className="mb-2">
              <Badge 
                variant={stats.is_data_consistent ? "default" : "destructive"}
                className="text-sm"
              >
                {stats.is_data_consistent ? "一致" : "不一致"}
              </Badge>
            </div>
            <p className="text-sm text-gray-600">
              {stats.is_data_consistent 
                ? "统计数据与实际数据一致" 
                : `差异: ${Math.abs(stats.total_prompts_generated - stats.real_time_count)}`
              }
            </p>
          </Card>

          {/* 数据源 */}
          <Card className="p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Database className="w-5 h-5 text-purple-500" />
              <h3 className="text-lg font-semibold">数据源</h3>
            </div>
            <div className="mb-2">
              <Badge 
                variant={
                  stats.data_source === 'database' ? 'default' : 
                  stats.data_source === 'default' ? 'secondary' : 'destructive'
                }
              >
                {stats.data_source === 'database' ? '数据库' : 
                 stats.data_source === 'default' ? '默认' : '离线'}
              </Badge>
            </div>
            <p className="text-sm text-gray-600">
              当前数据来源
            </p>
          </Card>

          {/* 最后更新时间 */}
          <Card className="p-6">
            <div className="flex items-center space-x-2 mb-4">
              <RefreshCw className="w-5 h-5 text-gray-500" />
              <h3 className="text-lg font-semibold">最后更新</h3>
            </div>
            <div className="text-lg font-semibold text-gray-700 mb-2">
              {new Date(stats.last_updated).toLocaleString()}
            </div>
            <p className="text-sm text-gray-600">
              统计数据最后更新时间
            </p>
          </Card>

          {/* 状态概览 */}
          <Card className="p-6">
            <div className="flex items-center space-x-2 mb-4">
              {stats.is_data_consistent && stats.data_source === 'database' ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <XCircle className="w-5 h-5 text-red-500" />
              )}
              <h3 className="text-lg font-semibold">系统状态</h3>
            </div>
            <div className="mb-2">
              <Badge 
                variant={
                  stats.is_data_consistent && stats.data_source === 'database' 
                    ? 'default' 
                    : 'destructive'
                }
              >
                {stats.is_data_consistent && stats.data_source === 'database' 
                  ? '正常' 
                  : '需要关注'
                }
              </Badge>
            </div>
            <p className="text-sm text-gray-600">
              {stats.is_data_consistent && stats.data_source === 'database' 
                ? '所有系统运行正常' 
                : '建议手动更新统计数据'
              }
            </p>
          </Card>
        </div>
      )}

      {/* 操作说明 */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">操作说明</h3>
        <div className="space-y-2 text-sm text-gray-600">
          <p>• <strong>刷新</strong>: 重新从数据库获取最新的统计数据</p>
          <p>• <strong>手动更新</strong>: 重新计算并更新统计数据，用于修复数据不一致问题</p>
          <p>• <strong>数据一致性</strong>: 检查统计表中的数据是否与实际 prompts 表数据一致</p>
          <p>• <strong>触发器</strong>: 正常情况下，每次插入新的 prompt 时会自动更新统计数据</p>
        </div>
      </Card>
    </div>
  );
} 