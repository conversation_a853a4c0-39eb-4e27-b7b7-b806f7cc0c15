"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  Settings,
  User,
  Shield,
  Palette,
  Bell,
  Database,
  Key,
  Trash2,
  Save,
  Edit,
  LogOut,
  Download,
  Upload,
  Eye,
  EyeOff,
  Al<PERSON><PERSON>riangle,
  CheckCircle,
  ExternalLink,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { useUserStore } from "@/lib/user-store";
import Link from "next/link";

// 定义类型
interface UserProfile {
  id: string;
  username: string | null;
  intent_model_name: string;
  generation_model_name: string;
  encrypted_intent_api_key: string | null;
  encrypted_generation_api_key: string | null;
  created_at: string;
  updated_at: string;
}

interface UserStats {
  totalPrompts: number;
  enhancedPrompts: number;
  generatedPrompts: number;
  joinDate: string;
}

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState("profile");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [stats, setStats] = useState<UserStats | null>(null);
  
  // 表单状态
  const [username, setUsername] = useState("");
  const [intentModel, setIntentModel] = useState("Gemini Pro");
  const [generationModel, setGenerationModel] = useState("GPT-4o");
  
  // 偏好设置
  const [theme, setTheme] = useState("dark");
  const [language, setLanguage] = useState("zh-CN");
  const [autoSave, setAutoSave] = useState(true);
  const [showTips, setShowTips] = useState(true);
  
  // 对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [logoutDialogOpen, setLogoutDialogOpen] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [confirmEmail, setConfirmEmail] = useState("");
  const [deleteLoading, setDeleteLoading] = useState(false);

  const { toast } = useToast();
  const { user, setUser } = useUserStore();
  const supabase = createClientComponentClient();

  // 获取用户资料
  const fetchProfile = async () => {
    if (!user) return;

    try {
      const response = await fetch("/api/user/profile");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "获取数据失败");
      }

      setProfile(data.profile);
      setUsername(data.profile.username || "");
      setIntentModel(data.profile.intent_model_name || "Gemini Pro");
      setGenerationModel(data.profile.generation_model_name || "GPT-4o");
      
      setStats({
        ...data.stats,
        joinDate: data.profile.created_at,
      });
    } catch (error) {
      console.error("获取数据失败:", error);
      toast({
        title: "获取数据失败",
        description: "请刷新页面重试",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 保存用户资料
  const saveProfile = async () => {
    if (!user || !profile) return;

    setSaving(true);
    try {
      const response = await fetch("/api/user/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          username,
          intent_model_name: intentModel,
          generation_model_name: generationModel,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "保存失败");
      }

      toast({
        title: "保存成功",
        description: "用户资料已更新",
      });

      // 重新获取资料
      fetchProfile();
    } catch (error) {
      console.error("保存失败:", error);
      toast({
        title: "保存失败",
        description: "请稍后重试",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // 导出用户数据
  const exportUserData = async () => {
    if (!user) return;

    try {
      const response = await fetch("/api/user/export");
      const exportData = await response.json();

      if (!response.ok) {
        throw new Error(exportData.error || "导出失败");
      }

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataUri = "data:application/json;charset=utf-8," + encodeURIComponent(dataStr);
      
      const linkElement = document.createElement("a");
      linkElement.setAttribute("href", dataUri);
      linkElement.setAttribute("download", `user-data-${new Date().toISOString().split('T')[0]}.json`);
      linkElement.click();

      toast({
        title: "导出成功",
        description: "用户数据已导出到文件",
      });
    } catch (error) {
      console.error("导出失败:", error);
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      });
    }
  };

  // 删除账户
  const deleteAccount = async () => {
    if (!user || confirmEmail !== user.email) {
      toast({
        title: "邮箱确认错误",
        description: "请输入正确的邮箱地址确认删除",
        variant: "destructive",
      });
      return;
    }

    setDeleteLoading(true);
    try {
      const response = await fetch("/api/user/delete", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          confirmEmail,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "删除失败");
      }

      toast({
        title: "账户数据已删除",
        description: "您的账户数据已被永久删除",
      });

      // 登出并重定向
      await supabase.auth.signOut();
      setUser(null);
      window.location.href = "/";
    } catch (error) {
      console.error("删除账户失败:", error);
      toast({
        title: "删除失败",
        description: "删除账户时出现错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setDeleteLoading(false);
    }
  };

  // 登出
  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      setUser(null);
      window.location.href = "/";
    } catch (error) {
      console.error("登出失败:", error);
      toast({
        title: "登出失败",
        description: "请稍后重试",
        variant: "destructive",
      });
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  useEffect(() => {
    if (user) {
      fetchProfile();
    }
  }, [user]);

  const tabs = [
    { id: "profile", label: "个人资料", icon: <User className="w-4 h-4" /> },
    { id: "preferences", label: "偏好设置", icon: <Palette className="w-4 h-4" /> },
    { id: "security", label: "安全设置", icon: <Shield className="w-4 h-4" /> },
    { id: "data", label: "数据管理", icon: <Database className="w-4 h-4" /> },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="w-16 h-16 rounded-full bg-gradient-to-r from-[#10b981] to-[#059669] flex items-center justify-center mx-auto mb-4 shadow-[0_0_20px_rgba(16,185,129,0.5)]">
            <Settings className="w-8 h-8 text-white animate-spin" />
          </div>
          <p className="text-gray-300 text-lg">加载设置中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-8 shadow-[0_8px_32px_rgba(0,0,0,0.3)]"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 rounded-2xl bg-gradient-to-r from-[#10b981] to-[#059669] shadow-[0_8px_32px_rgba(16,185,129,0.3)]">
              <Settings className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-[#10b981] to-[#059669] bg-clip-text text-transparent">
                设置
              </h1>
              <p className="text-gray-300 text-lg">
                管理你的账户设置和偏好
              </p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-lg font-semibold text-white">{username || "未设置"}</p>
            <p className="text-sm text-gray-400">
              {stats && `加入于 ${formatDate(stats.joinDate)}`}
            </p>
          </div>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 侧边栏导航 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="lg:col-span-1"
        >
          <div className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-3xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.3)]">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ${
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-[#10b981]/20 to-[#059669]/20 text-white border border-[#10b981]/30 shadow-[0_8px_32px_rgba(16,185,129,0.2)]"
                      : "text-gray-400 hover:text-white hover:bg-white/5"
                  }`}
                >
                  {tab.icon}
                  <span className="font-medium">{tab.label}</span>
                </button>
              ))}
            </nav>

            {/* 快速统计 */}
            {stats && (
              <div className="mt-6 pt-6 border-t border-white/10">
                <h3 className="text-sm font-semibold text-gray-400 mb-3">使用统计</h3>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">总提示词</span>
                    <span className="text-white font-medium">{stats.totalPrompts}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">智能增强</span>
                    <span className="text-white font-medium">{stats.enhancedPrompts}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">生成记录</span>
                    <span className="text-white font-medium">{stats.generatedPrompts}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </motion.div>

        {/* 主内容区域 */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="lg:col-span-3"
        >
          <div className="space-y-6">
            {/* 个人资料 */}
            {activeTab === "profile" && (
              <div className="space-y-6">
                <Card className="backdrop-blur-xl bg-white/5 border-white/10 shadow-[0_8px_32px_rgba(0,0,0,0.3)]">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-white">
                      <User className="w-5 h-5" />
                      <span>基本信息</span>
                    </CardTitle>
                    <CardDescription className="text-gray-400">
                      管理你的基本账户信息
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="text-gray-300">用户名</Label>
                        <Input
                          value={username}
                          onChange={(e) => setUsername(e.target.value)}
                          placeholder="请输入用户名"
                          className="bg-white/5 border-white/10 text-white placeholder-gray-400"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label className="text-gray-300">邮箱地址</Label>
                        <Input
                          value={user?.email || ""}
                          disabled
                          className="bg-white/5 border-white/10 text-gray-400"
                        />
                      </div>
                    </div>
                    
                    <Separator className="bg-white/10" />
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="text-gray-300">默认意图模型</Label>
                        <Select value={intentModel} onValueChange={setIntentModel}>
                          <SelectTrigger className="bg-white/5 border-white/10 text-white">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Gemini Pro">Gemini Pro</SelectItem>
                            <SelectItem value="GPT-4o">GPT-4o</SelectItem>
                            <SelectItem value="Claude 3.5">Claude 3.5</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label className="text-gray-300">默认生成模型</Label>
                        <Select value={generationModel} onValueChange={setGenerationModel}>
                          <SelectTrigger className="bg-white/5 border-white/10 text-white">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="GPT-4o">GPT-4o</SelectItem>
                            <SelectItem value="Claude 3.5">Claude 3.5</SelectItem>
                            <SelectItem value="Gemini Pro">Gemini Pro</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="flex justify-end space-x-3 pt-4">
                      <Button
                        onClick={saveProfile}
                        disabled={saving}
                        className="bg-gradient-to-r from-[#10b981] to-[#059669] hover:from-[#10b981]/80 hover:to-[#059669]/80"
                      >
                        <Save className="w-4 h-4 mr-2" />
                        {saving ? "保存中..." : "保存更改"}
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* AI 配置快捷入口 */}
                <Card className="backdrop-blur-xl bg-white/5 border-white/10 shadow-[0_8px_32px_rgba(0,0,0,0.3)]">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-white">
                      <Key className="w-5 h-5" />
                      <span>AI 模型配置</span>
                    </CardTitle>
                    <CardDescription className="text-gray-400">
                      管理你的AI模型提供商和API密钥
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-white font-medium">配置AI模型</p>
                        <p className="text-gray-400 text-sm">设置提供商、模型和API密钥</p>
                      </div>
                      <Link href="/app/config">
                        <Button variant="outline" className="border-white/20 text-gray-300 hover:bg-white/10">
                          <ExternalLink className="w-4 h-4 mr-2" />
                          前往配置
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* 偏好设置 */}
            {activeTab === "preferences" && (
              <Card className="backdrop-blur-xl bg-white/5 border-white/10 shadow-[0_8px_32px_rgba(0,0,0,0.3)]">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-white">
                    <Palette className="w-5 h-5" />
                    <span>偏好设置</span>
                  </CardTitle>
                  <CardDescription className="text-gray-400">
                    自定义你的使用体验
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label className="text-gray-300">主题</Label>
                      <Select value={theme} onValueChange={setTheme}>
                        <SelectTrigger className="bg-white/5 border-white/10 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="dark">深色模式</SelectItem>
                          <SelectItem value="light">浅色模式</SelectItem>
                          <SelectItem value="system">跟随系统</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-gray-300">语言</Label>
                      <Select value={language} onValueChange={setLanguage}>
                        <SelectTrigger className="bg-white/5 border-white/10 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="zh-CN">简体中文</SelectItem>
                          <SelectItem value="en-US">English</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <Separator className="bg-white/10" />

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-gray-300">自动保存</Label>
                        <p className="text-sm text-gray-400">自动保存你的提示词到灵感库</p>
                      </div>
                      <Switch
                        checked={autoSave}
                        onCheckedChange={setAutoSave}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-gray-300">显示使用提示</Label>
                        <p className="text-sm text-gray-400">在界面中显示操作提示和帮助信息</p>
                      </div>
                      <Switch
                        checked={showTips}
                        onCheckedChange={setShowTips}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end pt-4">
                    <Button className="bg-gradient-to-r from-[#10b981] to-[#059669] hover:from-[#10b981]/80 hover:to-[#059669]/80">
                      <Save className="w-4 h-4 mr-2" />
                      保存偏好
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 安全设置 */}
            {activeTab === "security" && (
              <div className="space-y-6">
                <Card className="backdrop-blur-xl bg-white/5 border-white/10 shadow-[0_8px_32px_rgba(0,0,0,0.3)]">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-white">
                      <Shield className="w-5 h-5" />
                      <span>账户安全</span>
                    </CardTitle>
                    <CardDescription className="text-gray-400">
                      管理你的账户安全设置
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-4 bg-green-500/10 border border-green-500/30 rounded-xl">
                      <div className="flex items-center space-x-2 text-green-400 mb-2">
                        <CheckCircle className="w-5 h-5" />
                        <span className="font-medium">账户安全状态良好</span>
                      </div>
                      <p className="text-sm text-green-300">
                        你的账户使用安全的认证方式，数据已加密存储
                      </p>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10">
                        <div>
                          <p className="text-white font-medium">邮箱验证</p>
                          <p className="text-gray-400 text-sm">你的邮箱已验证</p>
                        </div>
                        <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                          已验证
                        </Badge>
                      </div>
                      
                      <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10">
                        <div>
                          <p className="text-white font-medium">登出所有设备</p>
                          <p className="text-gray-400 text-sm">在所有设备上登出账户</p>
                        </div>
                        <Button
                          onClick={() => setLogoutDialogOpen(true)}
                          variant="outline"
                          className="border-white/20 text-gray-300 hover:bg-white/10"
                        >
                          <LogOut className="w-4 h-4 mr-2" />
                          登出
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* 数据管理 */}
            {activeTab === "data" && (
              <div className="space-y-6">
                <Card className="backdrop-blur-xl bg-white/5 border-white/10 shadow-[0_8px_32px_rgba(0,0,0,0.3)]">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-white">
                      <Database className="w-5 h-5" />
                      <span>数据管理</span>
                    </CardTitle>
                    <CardDescription className="text-gray-400">
                      导出或删除你的账户数据
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                        <div className="flex items-center space-x-3 mb-3">
                          <Download className="w-5 h-5 text-blue-400" />
                          <h3 className="font-medium text-white">导出数据</h3>
                        </div>
                        <p className="text-gray-400 text-sm mb-4">
                          下载你的所有账户数据，包括提示词、配置等
                        </p>
                        <Button
                          onClick={exportUserData}
                          variant="outline"
                          className="w-full border-blue-500/30 text-blue-400 hover:bg-blue-500/10"
                        >
                          <Download className="w-4 h-4 mr-2" />
                          导出数据
                        </Button>
                      </div>

                      <div className="p-4 bg-red-500/5 rounded-xl border border-red-500/30">
                        <div className="flex items-center space-x-3 mb-3">
                          <Trash2 className="w-5 h-5 text-red-400" />
                          <h3 className="font-medium text-white">删除账户</h3>
                        </div>
                        <p className="text-gray-400 text-sm mb-4">
                          永久删除你的账户和所有相关数据
                        </p>
                        <Button
                          onClick={() => setDeleteDialogOpen(true)}
                          variant="outline"
                          className="w-full border-red-500/30 text-red-400 hover:bg-red-500/10"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          删除账户
                        </Button>
                      </div>
                    </div>

                    {stats && (
                      <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                        <h3 className="font-medium text-white mb-3">数据概览</h3>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <p className="text-gray-400">提示词总数</p>
                            <p className="text-white font-medium">{stats.totalPrompts}</p>
                          </div>
                          <div>
                            <p className="text-gray-400">智能增强</p>
                            <p className="text-white font-medium">{stats.enhancedPrompts}</p>
                          </div>
                          <div>
                            <p className="text-gray-400">生成记录</p>
                            <p className="text-white font-medium">{stats.generatedPrompts}</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </motion.div>
      </div>

      {/* 删除账户确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={(open) => {
        setDeleteDialogOpen(open);
        if (!open) {
          setConfirmEmail("");
        }
      }}>
        <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2 text-red-400">
              <AlertTriangle className="w-5 h-5" />
              <span>删除账户</span>
            </DialogTitle>
            <DialogDescription className="text-gray-400">
              这是一个不可逆的操作。删除账户将：
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2 text-sm text-gray-300">
              <p>• 永久删除你的所有提示词记录</p>
              <p>• 删除你的AI配置和设置</p>
              <p>• 删除你的用户资料信息</p>
              <p>• 无法恢复任何数据</p>
            </div>

            <div className="space-y-2">
              <Label className="text-gray-300">请输入你的邮箱地址确认删除：</Label>
              <Input
                type="email"
                value={confirmEmail}
                onChange={(e) => setConfirmEmail(e.target.value)}
                placeholder={user?.email || ""}
                className="bg-white/5 border-white/10 text-white placeholder-gray-400"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="ghost"
              onClick={() => setDeleteDialogOpen(false)}
              className="text-gray-400 hover:text-white"
              disabled={deleteLoading}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={deleteAccount}
              disabled={deleteLoading || confirmEmail !== user?.email}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteLoading ? "删除中..." : "确认删除账户"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 登出确认对话框 */}
      <Dialog open={logoutDialogOpen} onOpenChange={setLogoutDialogOpen}>
        <DialogContent className="bg-gray-900 border-gray-700 text-white">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <LogOut className="w-5 h-5" />
              <span>确认登出</span>
            </DialogTitle>
            <DialogDescription className="text-gray-400">
              确定要在所有设备上登出吗？
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter>
            <Button
              variant="ghost"
              onClick={() => setLogoutDialogOpen(false)}
              className="text-gray-400 hover:text-white"
            >
              取消
            </Button>
            <Button
              onClick={handleLogout}
              className="bg-gradient-to-r from-[#10b981] to-[#059669] hover:from-[#10b981]/80 hover:to-[#059669]/80"
            >
              确认登出
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 