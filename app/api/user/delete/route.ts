import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function DELETE(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "未授权访问" },
        { status: 401 }
      );
    }

    // 获取确认密码或其他验证信息
    const body = await request.json();
    const { confirmEmail } = body;

    // 验证邮箱确认
    if (confirmEmail !== user.email) {
      return NextResponse.json(
        { error: "邮箱确认不匹配" },
        { status: 400 }
      );
    }

    try {
      // 按顺序删除用户相关数据（由于外键约束）
      // 1. 删除用户的提示词记录
      const { error: promptsError } = await supabase
        .from("prompts")
        .delete()
        .eq("user_id", user.id);

      if (promptsError) {
        console.error("删除提示词记录失败:", promptsError);
      }

      // 2. 删除用户的AI配置
      const { error: configError } = await supabase
        .from("user_ai_configs")
        .delete()
        .eq("user_id", user.id);

      if (configError) {
        console.error("删除AI配置失败:", configError);
      }

      // 3. 删除用户资料
      const { error: profileError } = await supabase
        .from("profiles")
        .delete()
        .eq("id", user.id);

      if (profileError) {
        console.error("删除用户资料失败:", profileError);
      }

      // 4. 最后删除认证用户（这会触发级联删除）
      // 注意：在客户端应用中，我们通常不直接删除auth.users
      // 而是让用户自己在客户端调用 supabase.auth.signOut() 然后删除账户
      // 或者使用 Supabase 的管理 API

      return NextResponse.json({
        message: "账户数据删除成功",
        deletedData: {
          prompts: !promptsError,
          config: !configError,
          profile: !profileError,
        },
      });
    } catch (error) {
      console.error("删除用户数据失败:", error);
      return NextResponse.json(
        { error: "删除用户数据失败" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("API错误:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
} 