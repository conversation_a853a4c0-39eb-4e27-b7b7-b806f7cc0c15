import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "未授权访问" },
        { status: 401 }
      );
    }

    // 获取用户资料
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", user.id)
      .single();

    if (profileError) {
      console.error("获取用户资料失败:", profileError);
      return NextResponse.json(
        { error: "获取用户资料失败" },
        { status: 500 }
      );
    }

    // 获取用户统计数据
    const { data: prompts, error: promptsError } = await supabase
      .from("prompts")
      .select("type, created_at")
      .eq("user_id", user.id);

    let stats = {
      totalPrompts: 0,
      enhancedPrompts: 0,
      generatedPrompts: 0,
    };

    if (!promptsError && prompts) {
      stats = {
        totalPrompts: prompts.length,
        enhancedPrompts: prompts.filter(p => p.type === "enhanced").length,
        generatedPrompts: prompts.filter(p => p.type === "generated").length,
      };
    }

    return NextResponse.json({
      profile,
      stats,
    });
  } catch (error) {
    console.error("API错误:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "未授权访问" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { username, intent_model_name, generation_model_name } = body;

    // 验证输入
    if (username && typeof username !== "string") {
      return NextResponse.json(
        { error: "用户名格式不正确" },
        { status: 400 }
      );
    }

    // 更新用户资料
    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    if (username !== undefined) {
      updateData.username = username;
    }
    if (intent_model_name !== undefined) {
      updateData.intent_model_name = intent_model_name;
    }
    if (generation_model_name !== undefined) {
      updateData.generation_model_name = generation_model_name;
    }

    const { data: updatedProfile, error: updateError } = await supabase
      .from("profiles")
      .update(updateData)
      .eq("id", user.id)
      .select()
      .single();

    if (updateError) {
      console.error("更新用户资料失败:", updateError);
      return NextResponse.json(
        { error: "更新用户资料失败" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "用户资料更新成功",
      profile: updatedProfile,
    });
  } catch (error) {
    console.error("API错误:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
} 