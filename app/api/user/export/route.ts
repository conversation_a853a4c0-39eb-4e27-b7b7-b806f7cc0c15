import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "未授权访问" },
        { status: 401 }
      );
    }

    // 并行获取所有用户数据
    const [profileResponse, promptsResponse, configResponse] = await Promise.all([
      supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single(),
      supabase
        .from("prompts")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false }),
      supabase
        .from("user_ai_configs")
        .select("*")
        .eq("user_id", user.id)
        .single(),
    ]);

    // 构建导出数据
    const exportData = {
      exportInfo: {
        exportDate: new Date().toISOString(),
        version: "1.0",
        userId: user.id,
        userEmail: user.email,
      },
      profile: profileResponse.data || null,
      prompts: promptsResponse.data || [],
      aiConfig: configResponse.data || null,
      statistics: {
        totalPrompts: promptsResponse.data?.length || 0,
        enhancedPrompts: promptsResponse.data?.filter(p => p.type === "enhanced").length || 0,
        generatedPrompts: promptsResponse.data?.filter(p => p.type === "generated").length || 0,
        joinDate: profileResponse.data?.created_at || null,
      },
    };

    // 检查是否有错误
    if (profileResponse.error && profileResponse.error.code !== 'PGRST116') {
      console.error("获取用户资料失败:", profileResponse.error);
    }
    
    if (promptsResponse.error) {
      console.error("获取提示词数据失败:", promptsResponse.error);
    }
    
    if (configResponse.error && configResponse.error.code !== 'PGRST116') {
      console.error("获取配置数据失败:", configResponse.error);
    }

    return NextResponse.json(exportData);
  } catch (error) {
    console.error("API错误:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
} 