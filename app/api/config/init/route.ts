import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // 获取当前用户
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    // 确保用户在 profiles 表中存在
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.id)
      .single()

    if (profileError && profileError.code === 'PGRST116') {
      // 用户不存在于 profiles 表中，创建一个
      const { error: createProfileError } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          username: user.user_metadata?.username || user.email?.split('@')[0] || 'User'
        })

      if (createProfileError) {
        console.error('创建用户资料失败:', createProfileError)
        return NextResponse.json({ error: '创建用户资料失败' }, { status: 500 })
      }
    } else if (profileError) {
      console.error('查询用户资料失败:', profileError)
      return NextResponse.json({ error: '查询用户资料失败' }, { status: 500 })
    }

    // 从 config.json 文件读取的配置数据
    const configData = {
   
    }

    // 插入或更新用户配置
    const { data, error } = await supabase
      .from('user_ai_configs')
      .upsert({
        user_id: user.id,
        config_data: configData
      })
      .select()

    if (error) {
      console.error('初始化配置失败:', error)
      return NextResponse.json({ error: '初始化配置失败' }, { status: 500 })
    }

    return NextResponse.json({ 
      message: '配置初始化成功', 
      data: data[0] 
    })

  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
} 