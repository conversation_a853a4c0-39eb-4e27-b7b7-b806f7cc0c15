import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "未授权访问" },
        { status: 401 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const type = searchParams.get("type"); // 'enhanced' 或 'generated' 或不传（全部）
    const search = searchParams.get("search"); // 搜索关键词

    // 构建查询
    let query = supabase
      .from("prompts")
      .select("*", { count: "exact" })
      .eq("user_id", user.id)
      .order("created_at", { ascending: false });

    // 按类型筛选
    if (type && (type === "enhanced" || type === "generated")) {
      query = query.eq("type", type);
    }

    // 搜索功能
    if (search) {
      query = query.or(`source_text.ilike.%${search}%,result_text.ilike.%${search}%`);
    }

    // 分页
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    const { data: prompts, error, count } = await query;

    if (error) {
      console.error("获取灵感库数据失败:", error);
      return NextResponse.json(
        { error: "获取灵感库数据失败" },
        { status: 500 }
      );
    }

    // 计算分页信息
    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      prompts: prompts || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error("API错误:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
} 