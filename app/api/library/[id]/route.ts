import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "未授权访问" },
        { status: 401 }
      );
    }

    const promptId = params.id;

    // 验证记录是否存在且属于当前用户
    const { data: prompt, error: fetchError } = await supabase
      .from("prompts")
      .select("id, user_id")
      .eq("id", promptId)
      .eq("user_id", user.id)
      .single();

    if (fetchError || !prompt) {
      return NextResponse.json(
        { error: "记录不存在或无权限删除" },
        { status: 404 }
      );
    }

    // 删除记录
    const { error: deleteError } = await supabase
      .from("prompts")
      .delete()
      .eq("id", promptId)
      .eq("user_id", user.id);

    if (deleteError) {
      console.error("删除记录失败:", deleteError);
      return NextResponse.json(
        { error: "删除记录失败" },
        { status: 500 }
      );
    }

    return NextResponse.json({ message: "删除成功" });
  } catch (error) {
    console.error("API错误:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "未授权访问" },
        { status: 401 }
      );
    }

    const promptId = params.id;

    // 获取记录详情
    const { data: prompt, error } = await supabase
      .from("prompts")
      .select("*")
      .eq("id", promptId)
      .eq("user_id", user.id)
      .single();

    if (error || !prompt) {
      return NextResponse.json(
        { error: "记录不存在或无权限访问" },
        { status: 404 }
      );
    }

    return NextResponse.json({ prompt });
  } catch (error) {
    console.error("API错误:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
} 