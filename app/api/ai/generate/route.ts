import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import OpenAI from "openai";
import { GoogleGenerativeAI } from "@google/generative-ai";

// 提示词系统消息
const intentSystemPrompt = `你是一个专业的意图分析专家，请用最精炼的语言（不超过100字）完成以下任务：

1. 核心需求：用1句话概括用户本质需求
2. 场景推断：用3-5个词描述最可能的使用场景
3. 隐含需求：用1句话点明用户未明说的潜在需求
4. 响应建议：用3-5个词提示最佳响应方向

要求：
- 严格区分事实陈述和你的推测
- 对模糊表述给出2种可能解释
- 特别关注情绪词和修饰词
- 使用"需求："、"场景："等明确标签`;

const promptSystemPrompt = `你是一位顶级的提示词工程专家(Prompt Engineering Expert)，拥有深刻的洞察力，能将任何模糊的用户需求转化为结构清晰、专业且高效的提示词。

你的工作流程分为两步：

**第一步：内部思考与分析（这部分内容不要在最终结果中输出）**
当你收到用户的原始输入时，请先在内部深入分析其背后的真实意图：
1.  **核心诉求**: 用户最想解决的核心问题是什么？
2.  **潜在场景**: 这个需求最可能在什么情况下使用？（例如：写周报、市场分析、学习新知识）
3.  **期望结果**: 用户期望得到什么形式的产出？（例如：表格、代码、文章、清单）
4.  **隐含需求**: 用户可能没有明说，但对结果质量至关重要的隐含要求是什么？（例如：需要正式的语气、要对初学者友好、内容需要有数据支撑）

**第二步：生成结构化提示词（这是你需要输出的唯一内容）**
基于以上深入分析，为用户生成一个专业、详尽且高质量的提示词。请严格遵循以下结构来组织提示词内容：

1.  **角色与目标 (Role & Goal)**: 定义AI需要扮演的专家角色，并阐明它的核心目标。
2.  **任务与指令 (Task & Instructions)**: 详细描述需要完成的具体任务，并提供清晰、分步骤的指令。
3.  **约束与限制 (Constraints & Limitations)**: 设定明确的边界条件，如字数、语气、禁止项等。
4.  **输出格式 (Output Format)**: 指定清晰的输出格式要求，如使用Markdown、JSON、列表等，并提供一个简短示例。
5.  **质量标准 (Quality Standards)**: 强调对输出结果的专业性、准确性、创造性等方面的要求。

---
请直接输出最终生成的、可供用户直接复制使用的完整提示词，不要包含你的分析过程或任何额外的解释。`;

// 根据 baseURL 和 API Key 判断真正的 provider
function getProviderFromConfig(baseURL?: string, apiKey?: string): string {
  if (!baseURL) {
    return "openai-compatible";
  }
  
  // 根据 baseURL 判断
  if (baseURL.includes("api.deepseek.com")) {
    return "deepseek";
  } else if (baseURL.includes("generativelanguage.googleapis.com")) {
    return "google";
  } else if (baseURL.includes("openrouter.ai")) {
    // 进一步检查 API Key 格式
    if (apiKey && apiKey.startsWith("sk-or-")) {
      return "openrouter";
    } else {
      // 可能是用 OpenRouter 代理其他服务，但 API Key 不是 OpenRouter 格式
      console.warn("检测到 OpenRouter baseURL 但 API Key 不是 sk-or- 格式，可能配置有误");
      return "openrouter";
    }
  } else if (baseURL.includes("api.openai.com")) {
    return "openai";
  } else {
    return "openai-compatible";
  }
}

// DeepSeek API 调用函数
async function callDeepSeekAPI(
  input: string,
  modelConfig: any
): Promise<{ intentAnalysis: string; generatedPrompt: string }> {
  try {
    console.log("DeepSeek API 调用", {
      model: modelConfig.model,
      title: modelConfig.title,
      apiKey: modelConfig.apiKey?.substring(0, 10) + "...",
      baseURL: modelConfig.baseURL,
    });

    // 检查 API Key
    if (!modelConfig.apiKey || modelConfig.apiKey === "your-deepseek-api-key") {
      throw new Error(
        "DeepSeek API Key 未配置或使用默认值，请在配置页面设置正确的 API Key"
      );
    }

    // 第一阶段：意图分析
    console.log("DeepSeek API 第一阶段：意图分析");
    const intentResponse = await fetch(`${modelConfig.baseURL}/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${modelConfig.apiKey}`,
      },
      body: JSON.stringify({
        model: modelConfig.model,
        messages: [
          { role: "system", content: intentSystemPrompt },
          { role: "user", content: input },
        ],
        temperature: 0.3,
        max_tokens: 500,
      }),
    });

    if (!intentResponse.ok) {
      const errorData = await intentResponse.json().catch(() => ({}));
      throw new Error(
        `DeepSeek API 调用失败: ${intentResponse.status} ${intentResponse.statusText}. ${
          errorData.error?.message || ""
        }`
      );
    }

    const intentData = await intentResponse.json();
    const intentAnalysis = intentData.choices[0]?.message?.content || "";

    if (!intentAnalysis) {
      throw new Error("DeepSeek API 返回的意图分析为空");
    }

    console.log(
      "DeepSeek 意图分析完成:",
      intentAnalysis.substring(0, 100) + "..."
    );

    // 第二阶段：基于意图分析生成完善的提示词
    console.log("DeepSeek API 第二阶段：生成提示词");
    const promptResponse = await fetch(`${modelConfig.baseURL}/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${modelConfig.apiKey}`,
      },
      body: JSON.stringify({
        model: modelConfig.model,
        messages: [
          { role: "system", content: promptSystemPrompt },
          { role: "user", content: `用户输入：${input}\n\n意图分析：${intentAnalysis}` },
        ],
        temperature: 0.5,
        max_tokens: 2000,
      }),
    });

    if (!promptResponse.ok) {
      const errorData = await promptResponse.json().catch(() => ({}));
      throw new Error(
        `DeepSeek API 调用失败: ${promptResponse.status} ${promptResponse.statusText}. ${
          errorData.error?.message || ""
        }`
      );
    }

    const promptData = await promptResponse.json();
    const generatedPrompt = promptData.choices[0]?.message?.content || "";

    if (!generatedPrompt) {
      throw new Error("DeepSeek API 返回的提示词为空");
    }

    console.log(
      "DeepSeek 提示词生成完成:",
      generatedPrompt.substring(0, 100) + "..."
    );

    return {
      intentAnalysis,
      generatedPrompt,
    };
  } catch (error: any) {
    console.error("DeepSeek API 调用错误:", error);
    throw new Error(`DeepSeek API 调用失败: ${error.message}`);
  }
}

// OpenAI 兼容 API 调用函数（包括 OpenRouter）
async function callOpenAICompatibleAPI(
  input: string,
  modelConfig: any,
  provider: string = "OpenAI"
): Promise<{ intentAnalysis: string; generatedPrompt: string }> {
  try {
    console.log(`${provider} API 调用`, {
      model: modelConfig.model,
      title: modelConfig.title,
      apiKey: modelConfig.apiKey?.substring(0, 10) + "...",
      baseURL: modelConfig.baseURL,
    });

    // 检查 API Key
    if (!modelConfig.apiKey || modelConfig.apiKey.includes("your-")) {
      throw new Error(
        `${provider} API Key 未配置或使用默认值，请在配置页面设置正确的 API Key`
      );
    }

    // 创建 OpenAI 客户端 - 根据 OpenRouter 文档配置
    const clientConfig: any = {
      apiKey: modelConfig.apiKey,
      baseURL: modelConfig.baseURL,
    };

    // 如果是 OpenRouter，添加额外的 headers
    if (provider === "OpenRouter") {
      clientConfig.defaultHeaders = {
        "HTTP-Referer": "https://ai-prompt-platform.com",
        "X-Title": "AI Prompt Platform",
      };
    }

    const openai = new OpenAI(clientConfig);

    // 第一阶段：意图分析
    console.log(`${provider} API 第一阶段：意图分析`);
    const intentResponse = await openai.chat.completions.create({
      model: modelConfig.model,
      messages: [
        { role: "system", content: intentSystemPrompt },
        { role: "user", content: input },
      ],
      temperature: 0.3,
      max_tokens: 500,
      // 对于 OpenRouter，可以添加额外的 headers
      ...(provider === "OpenRouter" && {
        extra_headers: {
          "HTTP-Referer": "https://ai-prompt-platform.com",
          "X-Title": "AI Prompt Platform",
        }
      })
    });

    const intentAnalysis = intentResponse.choices[0]?.message?.content || "";

    if (!intentAnalysis) {
      throw new Error(`${provider} API 返回的意图分析为空`);
    }

    console.log(
      `${provider} 意图分析完成:`,
      intentAnalysis.substring(0, 100) + "..."
    );

    // 第二阶段：基于意图分析生成完善的提示词
    console.log(`${provider} API 第二阶段：生成提示词`);
    const promptResponse = await openai.chat.completions.create({
      model: modelConfig.model,
      messages: [
        { role: "system", content: promptSystemPrompt },
        { role: "user", content: `用户输入：${input}\n\n意图分析：${intentAnalysis}` },
      ],
      temperature: 0.5,
      max_tokens: 2000,
      // 对于 OpenRouter，可以添加额外的 headers
      ...(provider === "OpenRouter" && {
        extra_headers: {
          "HTTP-Referer": "https://ai-prompt-platform.com",
          "X-Title": "AI Prompt Platform",
        }
      })
    });

    const generatedPrompt = promptResponse.choices[0]?.message?.content || "";

    if (!generatedPrompt) {
      throw new Error(`${provider} API 返回的提示词为空`);
    }

    console.log(
      `${provider} 提示词生成完成:`,
      generatedPrompt.substring(0, 100) + "..."
    );

    return {
      intentAnalysis,
      generatedPrompt,
    };
  } catch (error: any) {
    console.error(`${provider} API 调用错误:`, error);
    
    // 处理不同类型的错误
    if (error.status === 401) {
      // 特别处理 OpenRouter 的认证错误
      if (provider === "OpenRouter") {
        const apiKeyPrefix = modelConfig.apiKey?.substring(0, 6) || "";
        if (!apiKeyPrefix.startsWith("sk-or-")) {
          throw new Error(
            `OpenRouter API Key 格式错误：检测到 API Key 前缀为 "${apiKeyPrefix}"，但 OpenRouter 的 API Key 应该以 "sk-or-" 开头。请检查：\n` +
            `1. 确认使用的是 OpenRouter 的 API Key（https://openrouter.ai/keys）\n` +
            `2. 或者将 baseURL 改为对应服务的原生 API 地址\n` +
            `3. 确保账户有足够余额`
          );
        } else {
          throw new Error(
            `OpenRouter API Key 认证失败，请检查：\n` +
            `1. API Key 是否正确\n` +
            `2. 账户是否有足够余额\n` +
            `3. API Key 是否有相应权限`
          );
        }
      } else {
        throw new Error(`${provider} API Key 无效，请检查配置`);
      }
    } else if (error.status === 403) {
      throw new Error(`${provider} API 权限不足，请检查 API Key 权限`);
    } else if (error.status === 429) {
      throw new Error(`${provider} API 请求频率过高，请稍后重试`);
    } else if (error.status === 404) {
      throw new Error(`${provider} API 模型不存在，请检查模型名称`);
    } else {
      throw new Error(`${provider} API 调用失败: ${error.message}`);
    }
  }
}

// Google 原生 API 调用函数
async function callGoogleNativeAPI(
  input: string,
  modelConfig: any
): Promise<{ intentAnalysis: string; generatedPrompt: string }> {
  try {
    console.log("Google Native API 调用", {
      model: modelConfig.model,
      title: modelConfig.title,
      apiKey: modelConfig.apiKey?.substring(0, 10) + "...",
      baseURL: modelConfig.baseURL,
    });

    // 检查 API Key
    if (!modelConfig.apiKey || modelConfig.apiKey === "your-google-api-key") {
      throw new Error(
        "Google API Key 未配置或使用默认值，请在配置页面设置正确的 API Key"
      );
    }

    // 创建 Google AI 客户端
    const genAI = new GoogleGenerativeAI(modelConfig.apiKey);
    const model = genAI.getGenerativeModel({ model: modelConfig.model });

    // 第一阶段：意图分析
    console.log("Google Native API 第一阶段：意图分析");
    const intentResult = await model.generateContent({
      contents: [{ role: "user", parts: [{ text: `${intentSystemPrompt}\n\n用户输入：${input}` }] }],
      generationConfig: {
        temperature: 0.3,
        maxOutputTokens: 500,
      },
    });

    const intentAnalysis = intentResult.response.text();

    if (!intentAnalysis) {
      throw new Error("Google API 返回的意图分析为空");
    }

    console.log(
      "Google 意图分析完成:",
      intentAnalysis.substring(0, 100) + "..."
    );

    // 第二阶段：基于意图分析生成完善的提示词
    console.log("Google Native API 第二阶段：生成提示词");
    const promptResult = await model.generateContent({
      contents: [{ role: "user", parts: [{ text: `${promptSystemPrompt}\n\n用户输入：${input}\n\n意图分析：${intentAnalysis}` }] }],
      generationConfig: {
        temperature: 0.5,
        maxOutputTokens: 2000,
      },
    });

    const generatedPrompt = promptResult.response.text();

    if (!generatedPrompt) {
      throw new Error("Google API 返回的提示词为空");
    }

    console.log(
      "Google 提示词生成完成:",
      generatedPrompt.substring(0, 100) + "..."
    );

    return {
      intentAnalysis,
      generatedPrompt,
    };
  } catch (error: any) {
    console.error("Google Native API 调用错误:", error);
    
    // 处理不同类型的错误
    if (error.status === 401 || error.message.includes("API_KEY_INVALID")) {
      throw new Error("Google API Key 无效，请检查配置");
    } else if (error.status === 403) {
      throw new Error("Google API 权限不足，请检查 API Key 权限");
    } else if (error.status === 429) {
      throw new Error("Google API 请求频率过高，请稍后重试");
    } else if (error.message.includes("SAFETY")) {
      throw new Error("Google API 安全过滤：内容被安全策略阻止");
    } else {
      throw new Error(`Google API 调用失败: ${error.message}`);
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    const { input, provider, model } = await request.json();

    console.log("收到请求:", { input, provider, model });

    // 创建 Supabase 客户端
    const supabase = createRouteHandlerClient({
      cookies: () => cookies(),
    });

    // 获取用户信息
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "用户未登录" }, { status: 401 });
    }

    // 获取用户的 AI 配置
    const { data: configData, error: configError } = await supabase
      .from("user_ai_configs")
      .select("config_data")
      .eq("user_id", user.id)
      .single();

    if (configError || !configData) {
      return NextResponse.json(
        { error: "未找到用户配置，请先在配置页面设置 AI 模型" },
        { status: 404 }
      );
    }

    const config = configData.config_data;
    console.log("用户配置:", JSON.stringify(config, null, 2));

    // 查找指定的模型配置
    let modelConfig = null;
    
    // 配置结构是 providers 数组
    if (config.providers && Array.isArray(config.providers)) {
      for (const providerConfig of config.providers) {
        if (providerConfig.models && Array.isArray(providerConfig.models)) {
          const foundModel = providerConfig.models.find((m: any) => m.model === model);
          if (foundModel) {
            modelConfig = foundModel; // 直接使用找到的模型配置，因为 baseURL 和 apiKey 已经在模型内部
            break;
          }
        }
      }
    }

    if (!modelConfig) {
      console.error("未找到模型配置，可用模型:", 
        config.providers?.map((p: any) => 
          p.models?.map((m: any) => m.model)
        ).flat()
      );
      return NextResponse.json(
        { error: `未找到模型配置: ${model}` },
        { status: 404 }
      );
    }

    console.log("找到模型配置:", {
      model: modelConfig.model,
      title: modelConfig.title,
      baseURL: modelConfig.baseURL,
      hasApiKey: !!modelConfig.apiKey,
    });

    // 根据 baseURL 和 API Key 判断真正的 provider
    const actualProvider = getProviderFromConfig(modelConfig.baseURL, modelConfig.apiKey);
    console.log("实际 provider:", actualProvider);

    let result;

    // 根据实际 provider 调用对应的 API
    switch (actualProvider) {
      case "deepseek":
        result = await callDeepSeekAPI(input, modelConfig);
        break;
      case "google":
        result = await callGoogleNativeAPI(input, modelConfig);
        break;
      case "openrouter":
        result = await callOpenAICompatibleAPI(input, modelConfig, "OpenRouter");
        break;
      case "openai-compatible":
      default:
        result = await callOpenAICompatibleAPI(input, modelConfig, "OpenAI");
        break;
    }

    // 保存生成的提示词到数据库（登录用户才保存详细内容）
    if (result.generatedPrompt) {
      try {
        const { error: insertError } = await supabase.from("prompts").insert({
          user_id: user.id,
          source_text: input,
          result_text: result.generatedPrompt,
          type: "generated",
        });
        
        if (insertError) {
          console.error("保存生成结果到数据库失败:", insertError);
        } else {
          console.log("生成的提示词已保存到数据库，统计已自动更新");
        }
      } catch (dbError) {
        console.error("保存到数据库失败:", dbError);
        // 不影响主要功能，只记录错误
      }
    }

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    console.error("API错误:", error);
    return NextResponse.json(
      { error: error.message || "生成失败，请重试" },
      { status: 500 }
    );
  }
}
