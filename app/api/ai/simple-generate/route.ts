import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";

// 提示词系统消息
const intentSystemPrompt = `你是一个专业的AI助手，专门分析用户的真实意图。请仔细分析用户的输入，理解他们真正想要什么，然后提供简洁明确的意图分析。

分析要点：
1. 用户的核心需求是什么？
2. 用户可能的使用场景是什么？
3. 用户期望得到什么样的帮助？
4. 有什么隐含的需求没有明确表达？

请用简洁的语言回答，不要超过200字。`;

const promptSystemPrompt = `你是一个专业的提示词工程师，擅长将用户需求转化为详细、结构化、高质量的提示词。

基于前面的意图分析，请为用户生成一个完善的提示词，要求：

1. **角色定义**：明确AI助手的专业角色和能力
2. **任务描述**：清晰描述需要完成的具体任务
3. **输出格式**：指定期望的回答格式和结构
4. **质量要求**：设定专业水准和质量标准
5. **注意事项**：提醒重要的注意点和限制条件

生成的提示词应该：
- 结构清晰，逻辑严谨
- 专业性强，针对性好
- 易于理解和执行
- 能够产生高质量的输出

请直接输出完整的提示词，不需要额外的解释。`;

// 默认 DeepSeek 配置
const DEFAULT_DEEPSEEK_CONFIG = {
  model: "deepseek-chat",
  baseURL: "https://api.deepseek.com/v1",
  apiKey: process.env.DEEPSEEK_API_KEY || "your-deepseek-api-key",
  title: "DeepSeek Chat"
};

// DeepSeek API 调用函数
async function callDeepSeekAPI(
  input: string
): Promise<{ intentAnalysis: string; generatedPrompt: string }> {
  try {
    console.log("DeepSeek API 调用", {
      model: DEFAULT_DEEPSEEK_CONFIG.model,
      title: DEFAULT_DEEPSEEK_CONFIG.title,
      baseURL: DEFAULT_DEEPSEEK_CONFIG.baseURL,
      hasApiKey: !!DEFAULT_DEEPSEEK_CONFIG.apiKey,
    });

    // 检查 API Key
    if (!DEFAULT_DEEPSEEK_CONFIG.apiKey || DEFAULT_DEEPSEEK_CONFIG.apiKey === "your-deepseek-api-key") {
      throw new Error(
        "DeepSeek API Key 未配置，请在环境变量中设置 DEEPSEEK_API_KEY"
      );
    }

    // 第一阶段：意图分析
    console.log("DeepSeek API 第一阶段：意图分析");
    const intentResponse = await fetch(`${DEFAULT_DEEPSEEK_CONFIG.baseURL}/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${DEFAULT_DEEPSEEK_CONFIG.apiKey}`,
      },
      body: JSON.stringify({
        model: DEFAULT_DEEPSEEK_CONFIG.model,
        messages: [
          { role: "system", content: intentSystemPrompt },
          { role: "user", content: input },
        ],
        temperature: 0.3,
        max_tokens: 500,
      }),
    });

    if (!intentResponse.ok) {
      const errorData = await intentResponse.json().catch(() => ({}));
      throw new Error(
        `DeepSeek API 调用失败: ${intentResponse.status} ${intentResponse.statusText}. ${
          errorData.error?.message || ""
        }`
      );
    }

    const intentData = await intentResponse.json();
    const intentAnalysis = intentData.choices[0]?.message?.content || "";

    if (!intentAnalysis) {
      throw new Error("DeepSeek API 返回的意图分析为空");
    }

    console.log(
      "DeepSeek 意图分析完成:",
      intentAnalysis.substring(0, 100) + "..."
    );

    // 第二阶段：基于意图分析生成完善的提示词
    console.log("DeepSeek API 第二阶段：生成提示词");
    const promptResponse = await fetch(`${DEFAULT_DEEPSEEK_CONFIG.baseURL}/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${DEFAULT_DEEPSEEK_CONFIG.apiKey}`,
      },
      body: JSON.stringify({
        model: DEFAULT_DEEPSEEK_CONFIG.model,
        messages: [
          { role: "system", content: promptSystemPrompt },
          { role: "user", content: `用户输入：${input}\n\n意图分析：${intentAnalysis}` },
        ],
        temperature: 0.5,
        max_tokens: 2000,
      }),
    });

    if (!promptResponse.ok) {
      const errorData = await promptResponse.json().catch(() => ({}));
      throw new Error(
        `DeepSeek API 调用失败: ${promptResponse.status} ${promptResponse.statusText}. ${
          errorData.error?.message || ""
        }`
      );
    }

    const promptData = await promptResponse.json();
    const generatedPrompt = promptData.choices[0]?.message?.content || "";

    if (!generatedPrompt) {
      throw new Error("DeepSeek API 返回的提示词为空");
    }

    console.log(
      "DeepSeek 提示词生成完成:",
      generatedPrompt.substring(0, 100) + "..."
    );

    return {
      intentAnalysis,
      generatedPrompt,
    };
  } catch (error: any) {
    console.error("DeepSeek API 调用错误:", error);
    throw new Error(`DeepSeek API 调用失败: ${error.message}`);
  }
}

export async function POST(request: NextRequest) {
  try {
    const { input } = await request.json();

    console.log("收到简化请求:", { input });

    if (!input || typeof input !== 'string' || !input.trim()) {
      return NextResponse.json(
        { error: "请提供有效的输入内容" },
        { status: 400 }
      );
    }

    // 创建 Supabase 客户端
    const supabase = createRouteHandlerClient({
      cookies: () => cookies(),
    });

    // 获取用户信息
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    const isLoggedIn = !userError && user;

    // 调用 DeepSeek API
    const result = await callDeepSeekAPI(input);

    // 更新统计数据（包括未登录用户）
    if (result.generatedPrompt) {
      try {
        if (isLoggedIn) {
          // 登录用户：保存详细记录到数据库（触发器会自动更新统计）
          const { error: insertError } = await supabase.from("prompts").insert({
            user_id: user.id,
            source_text: input,
            result_text: result.generatedPrompt,
            type: "generated",
          });
          
          if (insertError) {
            console.error("保存登录用户数据失败:", insertError);
          } else {
            console.log("登录用户的提示词已保存到数据库，统计已自动更新");
          }
        } else {
          // 匿名用户：直接更新全局统计（使用服务端客户端）
          const serviceClient = createClient(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.SUPABASE_SERVICE_ROLE_KEY || ""
          );
          
          const { error: updateError } = await serviceClient
            .rpc('increment_anonymous_stats', { 
              stat_type: 'generated',
              increment_value: 1 
            });
          
          if (updateError) {
            console.error("更新匿名用户统计失败:", updateError);
          } else {
            console.log("匿名用户生成已计入统计");
          }
        }
      } catch (dbError) {
        console.error("数据库操作失败:", dbError);
        // 不影响主要功能，只记录错误
      }
    }

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    console.error("简化API错误:", error);
    return NextResponse.json(
      { error: error.message || "生成失败，请重试" },
      { status: 500 }
    );
  }
} 