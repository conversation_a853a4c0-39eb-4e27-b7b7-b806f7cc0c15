import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import OpenAI from "openai";
import { GoogleGenerativeAI } from "@google/generative-ai";

// 第一步：用户意图分析系统提示词
const domainAnalysisPrompt = `你是一个专业的用户输入分析专家，擅长从简短的输入中挖掘深层信息。

用户输入往往很简短，你需要分析并推测他们的具体需求和背景。

请分析用户输入，识别：
1. 具体实体 - 用户提到的具体事物（公司名、技术名词等）
2. 专业领域 - 所属的专业领域
3. 可能场景 - 用户可能面临的具体情况
4. 专业程度 - 推测用户的专业水平
5. 关键信息 - 需要在问题中体现的关键细节

请严格按照以下JSON格式输出，不要添加任何额外的文字说明：

{
  "specific_entity": "具体的实体名称",
  "domain": "专业领域",
  "likely_scenario": "用户可能面临的具体场景",
  "professional_level": "初级/中级/高级",
  "key_details": "需要在问题中体现的关键技术细节或专业信息",
  "confidence": 0.9
}

示例：
用户输入："远达环保"
{
  "specific_entity": "远达环保（600292）",
  "domain": "股票投资",
  "likely_scenario": "投资者想要分析这只环保股票的投资价值",
  "professional_level": "中级",
  "key_details": "主营脱硫脱硝业务，受碳中和政策影响，需要分析财务数据、行业前景、估值水平",
  "confidence": 0.95
}

用户输入："前端页面报错"
{
  "specific_entity": "前端页面错误",
  "domain": "前端开发",
  "likely_scenario": "开发者遇到页面加载或功能异常问题",
  "professional_level": "中级",
  "key_details": "可能涉及白屏、JavaScript错误、框架问题、打包资源、控制台调试",
  "confidence": 0.90
}

请只返回JSON格式的结果，不要包含任何其他文字。`;

// 第二步：专业问题生成系统提示词
const questionEnhancePrompt = `你是该领域的资深专家，专门为用户生成高质量的专业问题。

基于用户输入的分析结果，生成不同版本的专业问题，帮助用户深入探索这个话题。

版本要求：
1. 【简洁版】 - 1个直击核心的精准问题，包含具体实体名称
2. 【详细版】 - 3-5个包含专业背景、技术细节、具体场景的深度问题
3. 【分析版】 - 1个提供结构化分析框架的问题，包含1)2)3)4)等具体步骤

专业要求：
- 必须使用该领域的专业术语
- 问题要高度具体化，不能泛泛而谈
- 详细版的每个问题都要从不同角度深入
- 分析版必须提供可操作的分析框架或行动步骤
- 体现该领域的核心分析方法和工具

请严格按照以下JSON格式输出，不要添加任何额外的文字说明：

{
  "enhanced_questions": {
    "concise": "【简洁版】具体的核心问题",
    "detailed": [
      "详细问题1：从某个专业角度分析",
      "详细问题2：从另一个专业角度分析", 
      "详细问题3：从第三个专业角度分析",
      "详细问题4：从第四个专业角度分析",
      "详细问题5：从第五个专业角度分析"
    ],
    "analytical": "【分析版】结构化的分析框架：1) 步骤一 2) 步骤二 3) 步骤三 4) 步骤四"
  },
  "disclaimer": "如果是投资相关则包含风险提示，否则为空字符串"
}

示例：
基于分析：{"specific_entity": "远达环保（600292）", "domain": "股票投资", "key_details": "主营脱硫脱硝业务，受碳中和政策影响，需要分析财务数据、行业前景、估值水平"}

{
  "enhanced_questions": {
    "concise": "【简洁版】远达环保（600292）这只股票现在值得投资吗？",
    "detailed": [
      "远达环保的核心业务脱硫脱硝技术在当前环保政策下有哪些竞争优势和技术壁垒？",
      "从财务角度看，远达环保近三年的营收增长率、净利润率和ROE表现如何，是否符合价值投资标准？",
      "碳中和、碳达峰政策对远达环保的业务增长带来哪些具体机遇，市场空间有多大？",
      "与同花顺环保板块其他龙头企业相比，远达环保的估值水平（PE、PB、PEG）是否合理？",
      "远达环保面临的主要风险因素有哪些？政策变化、技术迭代、市场竞争如何影响其长期发展？"
    ],
    "analytical": "【分析版】如何为远达环保建立一个全面的投资分析框架？1) 宏观与行业分析：评估"碳中和"政策对其影响和行业发展趋势 2) 公司基本面：分析其历史财报、技术壁垒、管理层能力和业务护城河 3) 估值分析：使用市盈率（PE）、市净率（PB）、DCF等方法判断当前股价是否合理 4) 风险评估：识别其面临的政策、市场竞争和技术风险，制定风险控制策略"
  },
  "disclaimer": "风险提示：以上内容仅供参考，不构成投资建议。投资有风险，入市需谨慎。"
}

基于分析：{"specific_entity": "前端页面错误", "domain": "前端开发", "key_details": "可能涉及白屏、JavaScript错误、框架问题、打包资源、控制台调试"}

{
  "enhanced_questions": {
    "concise": "【简洁版】首页加载后出现白屏，如何排查？",
    "detailed": [
      "使用Vue3开发的SPA应用首页白屏，且控制台无任何报错信息，应该从哪些方面系统性地定位问题？",
      "前端页面白屏时，如何通过浏览器开发者工具的Network面板检查资源加载情况和API请求状态？",
      "当页面出现JavaScript运行时错误导致白屏时，如何利用Source Map和断点调试快速定位错误代码？",
      "在React/Vue等SPA框架中，路由配置错误、组件生命周期问题如何导致白屏，有什么调试技巧？"
    ],
    "analytical": "【分析版】前端页面白屏时，我应该按什么顺序进行系统调试？1) 基础检查：查看浏览器控制台的错误信息与网络请求状态，确认资源是否正常加载 2) DOM分析：检查页面DOM结构，确认根节点是否挂载成功，CSS样式是否正确应用 3) 代码调试：确认打包后的资源文件（JS/CSS）是否成功加载并执行，使用Source Map定位具体错误 4) 框架诊断：使用框架开发者工具检查组件状态、生命周期和数据流，排查业务逻辑问题"
  },
  "disclaimer": ""
}

请只返回JSON格式的结果，不要包含任何其他文字。`;

// 根据 baseURL 和 API Key 判断真正的 provider
function getProviderFromConfig(baseURL?: string, apiKey?: string): string {
  if (!baseURL) {
    return "openai-compatible";
  }

  // 根据 baseURL 判断
  if (baseURL.includes("api.deepseek.com")) {
    return "deepseek";
  } else if (baseURL.includes("generativelanguage.googleapis.com")) {
    return "google";
  } else if (baseURL.includes("openrouter.ai")) {
    // 进一步检查 API Key 格式
    if (apiKey && apiKey.startsWith("sk-or-")) {
      return "openrouter";
    } else {
      // 可能是用 OpenRouter 代理其他服务，但 API Key 不是 OpenRouter 格式
      console.warn(
        "检测到 OpenRouter baseURL 但 API Key 不是 sk-or- 格式，可能配置有误"
      );
      return "openrouter";
    }
  } else if (baseURL.includes("api.openai.com")) {
    return "openai";
  } else {
    return "openai-compatible";
  }
}

// 清理AI返回的文本，提取JSON部分
function cleanAndExtractJSON(text: string): string {
  // 移除可能的markdown代码块标记
  let cleaned = text.replace(/```json\s*/g, '').replace(/```\s*/g, '');
  
  // 移除开头和结尾的非JSON字符
  cleaned = cleaned.trim();
  
  // 移除可能的前后说明文字，只保留JSON部分
  const lines = cleaned.split('\n');
  let jsonLines: string[] = [];
  let inJson = false;
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    
    // 检测JSON开始
    if (trimmedLine.startsWith('{') && !inJson) {
      inJson = true;
      jsonLines.push(line);
    } else if (inJson) {
      jsonLines.push(line);
      // 检测JSON结束
      if (trimmedLine.endsWith('}')) {
        // 简单检查是否可能是JSON结束
        const jsonStr = jsonLines.join('\n');
        try {
          JSON.parse(jsonStr);
          // 如果能解析成功，说明JSON结束了
          break;
        } catch {
          // 继续收集行
        }
      }
    }
  }
  
  if (jsonLines.length > 0) {
    cleaned = jsonLines.join('\n');
  } else {
    // 如果上面的方法失败，回退到原来的方法
    const jsonStart = cleaned.indexOf('{');
    const jsonEnd = cleaned.lastIndexOf('}');
    
    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      cleaned = cleaned.substring(jsonStart, jsonEnd + 1);
    }
  }
  
  return cleaned.trim();
}

// 第一步：用户意图理解
async function performDomainIntentAnalysis(
  input: string,
  modelConfig: any,
  actualProvider: string
): Promise<{
  specific_entity: string;
  domain: string;
  likely_scenario: string;
  professional_level: string;
  key_details: string;
  confidence: number;
}> {
  let result;

  switch (actualProvider) {
    case "deepseek":
      result = await callDeepSeekAPI(input, modelConfig, domainAnalysisPrompt);
      break;
    case "google":
      result = await callGoogleNativeAPI(
        input,
        modelConfig,
        domainAnalysisPrompt
      );
      break;
    case "openrouter":
      result = await callOpenAICompatibleAPI(
        input,
        modelConfig,
        "OpenRouter",
        domainAnalysisPrompt
      );
      break;
    case "openai-compatible":
    default:
      result = await callOpenAICompatibleAPI(
        input,
        modelConfig,
        "OpenAI",
        domainAnalysisPrompt
      );
      break;
  }

  console.log("第一步原始返回结果:", result);

  // 清理和提取JSON
  const cleanedResult = cleanAndExtractJSON(result);
  console.log("第一步清理后结果:", cleanedResult);

  // 尝试解析JSON
  try {
    const parsedResult = JSON.parse(cleanedResult);
    return {
      specific_entity: parsedResult.specific_entity || "未知实体",
      domain: parsedResult.domain || "通用领域",
      likely_scenario: parsedResult.likely_scenario || "信息咨询",
      professional_level: parsedResult.professional_level || "初级",
      key_details: parsedResult.key_details || "",
      confidence: parsedResult.confidence || 0.5,
    };
  } catch (parseError) {
    console.warn("意图分析JSON解析失败:", parseError);
    console.warn("清理后的文本:", cleanedResult);
    return {
      specific_entity: "未知实体",
      domain: "通用领域",
      likely_scenario: "信息咨询",
      professional_level: "初级",
      key_details: "",
      confidence: 0.5,
    };
  }
}

// 第二步：问题增强
async function performQuestionEnhancement(
  originalInput: string,
  analysisResult: any,
  modelConfig: any,
  actualProvider: string
): Promise<any> {
  const enhanceInput = JSON.stringify({
    original_input: originalInput,
    analysis: analysisResult
  });

  let result;

  switch (actualProvider) {
    case "deepseek":
      result = await callDeepSeekAPI(
        enhanceInput,
        modelConfig,
        questionEnhancePrompt
      );
      break;
    case "google":
      result = await callGoogleNativeAPI(
        enhanceInput,
        modelConfig,
        questionEnhancePrompt
      );
      break;
    case "openrouter":
      result = await callOpenAICompatibleAPI(
        enhanceInput,
        modelConfig,
        "OpenRouter",
        questionEnhancePrompt
      );
      break;
    case "openai-compatible":
    default:
      result = await callOpenAICompatibleAPI(
        enhanceInput,
        modelConfig,
        "OpenAI",
        questionEnhancePrompt
      );
      break;
  }

  console.log("第二步原始返回结果:", result);

  // 清理和提取JSON
  const cleanedResult = cleanAndExtractJSON(result);
  console.log("第二步清理后结果:", cleanedResult);

  // 尝试解析JSON
  try {
    const parsedResult = JSON.parse(cleanedResult);
    // 确保返回的数据结构符合前端期望
    return {
      enhanced_questions: parsedResult.enhanced_questions || {
        concise: "【简洁版】具体的核心问题",
        detailed: [
          "详细问题1：从某个专业角度分析",
          "详细问题2：从另一个专业角度分析", 
          "详细问题3：从第三个专业角度分析",
          "详细问题4：从第四个专业角度分析",
          "详细问题5：从第五个专业角度分析"
        ],
        analytical: "【分析版】结构化的分析框架：1) 步骤一 2) 步骤二 3) 步骤三 4) 步骤四"
      },
      disclaimer: parsedResult.disclaimer || ""
    };
  } catch (parseError) {
    console.warn("问题增强JSON解析失败:", parseError);
    console.warn("清理后的文本:", cleanedResult);
    return generateDefaultEnhancedQuestions(originalInput, analysisResult.domain, analysisResult.key_details);
  }
}

// 生成默认增强问题（当JSON解析失败时使用）
function generateDefaultEnhancedQuestions(
  originalInput: string,
  domain: string,
  key_details: string
) {
  const isFinancial =
    domain.includes("金融") ||
    domain.includes("投资") ||
    domain.includes("股票");

  return {
    enhanced_questions: {
      concise: `【简洁版】关于"${originalInput}"的核心问题是什么？`,
      detailed: [
        `【详细版】我想深入了解"${originalInput}"的相关情况，包括具体的背景信息、当前状态以及可能的解决方案或发展趋势。`,
        `【详细版】${key_details}`,
        `【详细版】${key_details}`,
        `【详细版】${key_details}`,
        `【详细版】${key_details}`
      ],
      analytical: `【分析版】如何系统性地分析"${originalInput}"？1) 基础情况了解 2) 深入分析相关因素 3) 评估不同方案或方法 4) 制定具体的行动计划`
    },
    disclaimer: isFinancial
      ? "风险提示：以上内容仅供参考，不构成投资建议。投资有风险，入市需谨慎。"
      : "",
  };
}

// DeepSeek API 调用函数
async function callDeepSeekAPI(
  input: string,
  modelConfig: any,
  systemPrompt: string
): Promise<string> {
  try {
    console.log("DeepSeek API 调用");

    // 检查 API Key
    if (!modelConfig.apiKey || modelConfig.apiKey === "your-deepseek-api-key") {
      throw new Error(
        "DeepSeek API Key 未配置或使用默认值，请在配置页面设置正确的 API Key"
      );
    }

    const response = await fetch(`${modelConfig.baseURL}/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${modelConfig.apiKey}`,
      },
      body: JSON.stringify({
        model: modelConfig.model,
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: input },
        ],
        temperature: 0.7,
        max_tokens: 3000,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        `DeepSeek API 调用失败: ${response.status} ${response.statusText}. ${
          errorData.error?.message || ""
        }`
      );
    }

    const data = await response.json();
    const content = data.choices[0]?.message?.content || "";

    if (!content) {
      throw new Error("DeepSeek API 返回的内容为空");
    }

    return content;
  } catch (error: any) {
    console.error("DeepSeek API 调用错误:", error);
    throw new Error(`DeepSeek API 调用失败: ${error.message}`);
  }
}

// OpenAI 兼容 API 调用函数（包括 OpenRouter）
async function callOpenAICompatibleAPI(
  input: string,
  modelConfig: any,
  provider: string = "OpenAI",
  systemPrompt: string
): Promise<string> {
  try {
    console.log(`${provider} API 调用`);

    // 检查 API Key
    if (!modelConfig.apiKey || modelConfig.apiKey.includes("your-")) {
      throw new Error(
        `${provider} API Key 未配置或使用默认值，请在配置页面设置正确的 API Key`
      );
    }

    // 创建 OpenAI 客户端
    const clientConfig: any = {
      apiKey: modelConfig.apiKey,
      baseURL: modelConfig.baseURL,
    };

    // 如果是 OpenRouter，添加额外的 headers
    if (provider === "OpenRouter") {
      clientConfig.defaultHeaders = {
        "HTTP-Referer": "https://ai-prompt-platform.com",
        "X-Title": "AI Prompt Platform",
      };
    }

    const openai = new OpenAI(clientConfig);

    const response = await openai.chat.completions.create({
      model: modelConfig.model,
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: input },
      ],
      temperature: 0.7,
      max_tokens: 3000,
      // 对于 OpenRouter，可以添加额外的 headers
      ...(provider === "OpenRouter" && {
        extra_headers: {
          "HTTP-Referer": "https://ai-prompt-platform.com",
          "X-Title": "AI Prompt Platform",
        },
      }),
    });

    const content = response.choices[0]?.message?.content || "";

    if (!content) {
      throw new Error(`${provider} API 返回的内容为空`);
    }

    return content;
  } catch (error: any) {
    console.error(`${provider} API 调用错误:`, error);

    // 处理不同类型的错误
    if (error.status === 401) {
      // 特别处理 OpenRouter 的认证错误
      if (provider === "OpenRouter") {
        const apiKeyPrefix = modelConfig.apiKey?.substring(0, 6) || "";
        if (!apiKeyPrefix.startsWith("sk-or-")) {
          throw new Error(
            `OpenRouter API Key 格式错误：检测到 API Key 前缀为 "${apiKeyPrefix}"，但 OpenRouter 的 API Key 应该以 "sk-or-" 开头。请检查：\n` +
              `1. 确认使用的是 OpenRouter 的 API Key（https://openrouter.ai/keys）\n` +
              `2. 或者将 baseURL 改为对应服务的原生 API 地址\n` +
              `3. 确保账户有足够余额`
          );
        } else {
          throw new Error(
            `OpenRouter API Key 认证失败，请检查：\n` +
              `1. API Key 是否正确\n` +
              `2. 账户是否有足够余额\n` +
              `3. API Key 是否有相应权限`
          );
        }
      } else {
        throw new Error(`${provider} API Key 无效，请检查配置`);
      }
    } else if (error.status === 403) {
      throw new Error(`${provider} API 权限不足，请检查 API Key 权限`);
    } else if (error.status === 429) {
      throw new Error(`${provider} API 请求频率过高，请稍后重试`);
    } else if (error.status === 404) {
      throw new Error(`${provider} API 模型不存在，请检查模型名称`);
    } else {
      throw new Error(`${provider} API 调用失败: ${error.message}`);
    }
  }
}

// Google 原生 API 调用函数
async function callGoogleNativeAPI(
  input: string,
  modelConfig: any,
  systemPrompt: string
): Promise<string> {
  try {
    console.log("Google Native API 调用");

    // 检查 API Key
    if (!modelConfig.apiKey || modelConfig.apiKey === "your-google-api-key") {
      throw new Error(
        "Google API Key 未配置或使用默认值，请在配置页面设置正确的 API Key"
      );
    }

    // 创建 Google AI 客户端
    const genAI = new GoogleGenerativeAI(modelConfig.apiKey);
    const model = genAI.getGenerativeModel({ model: modelConfig.model });

    const result = await model.generateContent({
      contents: [
        {
          role: "user",
          parts: [{ text: `${systemPrompt}\n\n用户输入：${input}` }],
        },
      ],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 3000,
      },
    });

    const content = result.response.text();

    if (!content) {
      throw new Error("Google API 返回的内容为空");
    }

    return content;
  } catch (error: any) {
    console.error("Google Native API 调用错误:", error);

    // 处理不同类型的错误
    if (error.status === 401 || error.message.includes("API_KEY_INVALID")) {
      throw new Error("Google API Key 无效，请检查配置");
    } else if (error.status === 403) {
      throw new Error("Google API 权限不足，请检查 API Key 权限");
    } else if (error.status === 429) {
      throw new Error("Google API 请求频率过高，请稍后重试");
    } else if (error.message.includes("SAFETY")) {
      throw new Error("Google API 安全过滤：内容被安全策略阻止");
    } else {
      throw new Error(`Google API 调用失败: ${error.message}`);
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { input, provider, model, step } = body;

    console.log("收到请求:", { input, provider, model, step });

    // 验证输入
    if (!input || typeof input !== "string") {
      return NextResponse.json(
        { error: "请提供有效的输入内容" },
        { status: 400 }
      );
    }

    if (!provider || !model) {
      return NextResponse.json(
        { error: "请选择AI提供商和模型" },
        { status: 400 }
      );
    }

    // 创建 Supabase 客户端
    const supabase = createRouteHandlerClient({
      cookies: () => cookies(),
    });

    // 获取用户信息
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "用户未登录" }, { status: 401 });
    }

    // 获取用户的AI配置
    const { data: configData, error: configError } = await supabase
      .from("user_ai_configs")
      .select("config_data")
      .eq("user_id", user.id)
      .single();

    if (configError || !configData) {
      console.error("获取用户配置失败:", configError);
      return NextResponse.json(
        { error: "未找到用户配置，请先在配置页面设置 AI 模型" },
        { status: 404 }
      );
    }

    const config = configData.config_data;

    // 查找指定的模型配置
    let modelConfig = null;

    if (config.providers && Array.isArray(config.providers)) {
      for (const providerConfig of config.providers) {
        if (providerConfig.models && Array.isArray(providerConfig.models)) {
          const foundModel = providerConfig.models.find(
            (m: any) => m.model === model
          );
          if (foundModel) {
            modelConfig = foundModel;
            break;
          }
        }
      }
    }

    if (!modelConfig) {
      console.error("未找到模型配置");
      return NextResponse.json(
        { error: `未找到模型配置: ${model}` },
        { status: 404 }
      );
    }

    // 获取真正的提供商类型
    const actualProvider = getProviderFromConfig(
      modelConfig.baseURL,
      modelConfig.apiKey
    );

    // 执行完整的两步流程
    const analysisResult = await performDomainIntentAnalysis(
      input,
      modelConfig,
      actualProvider
    );
    console.log("第一步：领域和意图识别", analysisResult);

    const enhancementResult = await performQuestionEnhancement(
      input,
      analysisResult,
      modelConfig,
      actualProvider
    );
    console.log("第二步：问题增强", enhancementResult);

    // 保存增强的提示词到数据库（登录用户才保存详细内容）
    if (enhancementResult.enhanced_questions) {
      try {
        // 将增强的问题转换为字符串格式保存
        const enhancedText = [
          enhancementResult.enhanced_questions.concise,
          ...enhancementResult.enhanced_questions.detailed,
          enhancementResult.enhanced_questions.analytical
        ].join('\n\n');

        const { error: insertError } = await supabase.from("prompts").insert({
          user_id: user.id,
          source_text: input,
          result_text: enhancedText,
          type: "enhanced",
        });
        
        if (insertError) {
          console.error("保存增强结果到数据库失败:", insertError);
        } else {
          console.log("增强的提示词已保存到数据库，统计已自动更新");
        }
      } catch (dbError) {
        console.error("保存到数据库失败:", dbError);
        // 不影响主要功能，只记录错误
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        original_input: input,
        analysis: {
          specific_entity: analysisResult.specific_entity,
          domain: analysisResult.domain,
          likely_scenario: analysisResult.likely_scenario,
          professional_level: analysisResult.professional_level,
          key_details: analysisResult.key_details,
          confidence: analysisResult.confidence,
        },
        enhancement: {
          enhanced_questions: enhancementResult.enhanced_questions,
          disclaimer: enhancementResult.disclaimer
        },
        provider: actualProvider,
        model: modelConfig.model,
      },
    });
  } catch (error: any) {
    console.error("API错误:", error);
    return NextResponse.json(
      {
        error: error.message || "处理失败，请稍后重试",
        details: error.toString(),
      },
      { status: 500 }
    );
  }
}
