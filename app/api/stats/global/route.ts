import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 获取全局统计数据
    const { data: globalStats, error: statsError } = await supabase
      .from("global_stats")
      .select("*")
      .single();

    if (statsError) {
      console.error("获取全局统计数据失败:", statsError);
      
      // 如果表不存在或没有数据，返回默认值
      if (statsError.code === 'PGRST116') {
        return NextResponse.json({
          total_prompts_generated: 14321,
          last_updated: new Date().toISOString(),
          message: "使用默认数据，数据库中暂无统计记录"
        });
      }
      
      return NextResponse.json(
        { error: "获取统计数据失败" },
        { status: 500 }
      );
    }

    // 获取详细的统计数据（登录用户 + 匿名用户）
    const [
      { count: loggedTotalCount },
      { count: loggedEnhancedCount },
      { count: loggedGeneratedCount }
    ] = await Promise.all([
      supabase.from("prompts").select("*", { count: "exact", head: true }),
      supabase.from("prompts").select("*", { count: "exact", head: true }).eq("type", "enhanced"),
      supabase.from("prompts").select("*", { count: "exact", head: true }).eq("type", "generated")
    ]);

    const loggedTotal = loggedTotalCount || 0;
    const loggedEnhanced = loggedEnhancedCount || 0;
    const loggedGenerated = loggedGeneratedCount || 0;

    // 获取匿名用户统计
    const anonymousEnhanced = globalStats?.total_enhanced_anonymous || 0;
    const anonymousGenerated = globalStats?.total_generated_anonymous || 0;
    const anonymousTotal = anonymousEnhanced + anonymousGenerated;

    // 计算真实总数
    const actualEnhanced = loggedEnhanced + anonymousEnhanced;
    const actualGenerated = loggedGenerated + anonymousGenerated;
    const actualTotal = loggedTotal + anonymousTotal;

    // 检查数据一致性
    const storedCount = globalStats?.total_prompts_generated || 0;
    const isConsistent = Math.abs(storedCount - actualTotal) <= 1; // 允许1的误差（可能由于并发）

    // 获取最近7天的生成趋势
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const { data: recentPrompts } = await supabase
      .from("prompts")
      .select("created_at, type")
      .gte("created_at", sevenDaysAgo.toISOString())
      .order("created_at", { ascending: false });

    // 按天统计最近7天的数据
    const dailyStats = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      const dayPrompts = recentPrompts?.filter(p => 
        p.created_at.startsWith(dateStr)
      ) || [];
      
      return {
        date: dateStr,
        total: dayPrompts.length,
        enhanced: dayPrompts.filter(p => p.type === 'enhanced').length,
        generated: dayPrompts.filter(p => p.type === 'generated').length
      };
    }).reverse();

    return NextResponse.json({
      total_prompts_generated: globalStats?.total_prompts_generated || actualTotal,
      last_updated: globalStats?.last_updated || new Date().toISOString(),
      real_time_count: actualTotal,
      is_data_consistent: isConsistent,
      data_source: globalStats ? "database" : "default",
      breakdown: {
        enhanced: actualEnhanced,
        generated: actualGenerated,
        total: actualTotal,
        logged_users: {
          enhanced: loggedEnhanced,
          generated: loggedGenerated,
          total: loggedTotal
        },
        anonymous_users: {
          enhanced: anonymousEnhanced,
          generated: anonymousGenerated,
          total: anonymousTotal
        }
      },
      recent_trend: dailyStats,
      growth_rate: recentPrompts ? Math.round((recentPrompts.length / 7) * 100) / 100 : 0 // 平均每天增长数
    });

  } catch (error) {
    console.error("全局统计API错误:", error);
    
    // 发生错误时返回默认数据
    return NextResponse.json({
      total_prompts_generated: 14321,
      last_updated: new Date().toISOString(),
      error: "服务器错误，使用默认数据",
      data_source: "fallback"
    });
  }
}

// 手动触发统计数据更新的 POST 方法（仅供管理员使用）
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 获取当前用户（可以添加管理员权限检查）
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "未授权访问" },
        { status: 401 }
      );
    }

    // 获取实际的提示词数量（只统计登录用户的）
    const { count: promptsCount, error: countError } = await supabase
      .from("prompts")
      .select("*", { count: "exact", head: true });

    if (countError) {
      return NextResponse.json(
        { error: "获取提示词数量失败" },
        { status: 500 }
      );
    }

    const loggedUserCount = promptsCount || 0;

    // 获取当前全局统计数据以保留匿名用户统计
    const { data: currentStats } = await supabase
      .from("global_stats")
      .select("total_enhanced_anonymous, total_generated_anonymous")
      .eq("id", 1)
      .single();

    const anonymousCount = (currentStats?.total_enhanced_anonymous || 0) + 
                          (currentStats?.total_generated_anonymous || 0);
    const totalCount = loggedUserCount + anonymousCount;

    // 更新全局统计
    const { data: updatedStats, error: updateError } = await supabase
      .from("global_stats")
      .upsert({
        id: 1,
        total_prompts_generated: totalCount,
        last_updated: new Date().toISOString()
      })
      .select()
      .single();

    if (updateError) {
      console.error("更新全局统计失败:", updateError);
      return NextResponse.json(
        { error: "更新统计数据失败" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "统计数据已更新",
      data: updatedStats,
      logged_user_count: loggedUserCount,
      anonymous_count: anonymousCount,
      total_count: totalCount
    });

  } catch (error) {
    console.error("手动更新统计API错误:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
} 