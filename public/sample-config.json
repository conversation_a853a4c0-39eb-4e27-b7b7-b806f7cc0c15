{"providers": [{"provider": "deepseek", "models": [{"model": "deepseek-chat", "title": "DeepSeek Chat", "baseURL": "https://api.deepseek.com", "apiKey": "your-deepseek-api-key", "features": ["text"]}]}, {"provider": "openai", "models": [{"model": "openai/gpt-4o", "title": "GPT-4o (OpenRouter)", "baseURL": "https://openrouter.ai/api/v1", "apiKey": "your-openrouter-api-key", "features": ["vision", "text"]}, {"model": "openai/gpt-4o-mini", "title": "GPT-4o Mini (OpenRouter)", "baseURL": "https://openrouter.ai/api/v1", "apiKey": "your-openrouter-api-key", "features": ["text"]}]}, {"provider": "anthropic", "models": [{"model": "anthropic/claude-3-5-sonnet", "title": "Claude 3.5 Sonnet (OpenRouter)", "baseURL": "https://openrouter.ai/api/v1", "apiKey": "your-openrouter-api-key", "features": ["vision", "text"]}, {"model": "anthropic/claude-3-5-haiku", "title": "Claude 3.5 <PERSON><PERSON> (OpenRouter)", "baseURL": "https://openrouter.ai/api/v1", "apiKey": "your-openrouter-api-key", "features": ["text"]}]}, {"provider": "google", "models": [{"model": "gemini-2.5-flash", "title": "Gemini 2.5 Flash", "baseURL": "https://generativelanguage.googleapis.com/v1beta", "apiKey": "your-google-api-key", "features": ["vision", "text"]}, {"model": "gemini-1.5-flash", "title": "Gemini 1.5 Flash", "baseURL": "https://generativelanguage.googleapis.com/v1beta", "apiKey": "your-google-api-key", "features": ["vision", "text"]}, {"model": "gemini-1.5-pro", "title": "Gemini 1.5 Pro", "baseURL": "https://generativelanguage.googleapis.com/v1beta", "apiKey": "your-google-api-key", "features": ["vision", "text"]}]}, {"provider": "meta", "models": [{"model": "meta-llama/llama-3.2-90b-vision-instruct", "title": "Llama 3.2 90B Vision (OpenRouter)", "baseURL": "https://openrouter.ai/api/v1", "apiKey": "your-openrouter-api-key", "features": ["vision", "text"]}, {"model": "meta-llama/llama-3.1-70b-instruct", "title": "Llama 3.1 70B (OpenRouter)", "baseURL": "https://openrouter.ai/api/v1", "apiKey": "your-openrouter-api-key", "features": ["text"]}]}, {"provider": "mistral", "models": [{"model": "mistralai/mistral-large", "title": "<PERSON><PERSON><PERSON> Large (OpenRouter)", "baseURL": "https://openrouter.ai/api/v1", "apiKey": "your-openrouter-api-key", "features": ["text"]}]}]}